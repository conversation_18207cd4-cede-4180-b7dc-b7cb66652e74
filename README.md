```sh
1. docker compose up -d
2. npm install
3. npm run start:dev
4. npm run seed
```

```sh
docker compose up -d
docker compose down -v
```

```sh
curl --location 'http://127.0.0.1:3000/api/auth/create-super-admin' \
--header 'x-api-key: ASHATECH' \
--header 'Content-Type: application/json' \
--data '{
    "username": "admin",
    "password": "admin",
    "code": "0",
    "firstName": "Admin",
    "lastName": "Admin",
    "roleId": 1
}'
```

```sh
npm run migration:generate --name=create_counter_table

npm run migration:run
```
nest g res <name component>
