# User-Store Management API Documentation

## Overview
ระบบการจัดการความสัมพันธ์ระหว่าง User และ Store ที่ช่วยให้ User สามารถรับผิดชอบได้หลาย Store และ Store สามารถมี User หลายคนรับผิดชอบได้

## User Management APIs

### 1. ดึงรายการ Store ที่ User รับผิดชอบ
```http
GET /user/{id}/stores
```

**Response:**
```json
[
  {
    "id": 1,
    "code": "ST001",
    "name": "ร้านค้าสาขา 1",
    "address": "123 ถนนสุขุมวิท"
  }
]
```

### 2. มอบหมาย Store ให้ User
```http
POST /user/{id}/stores
Content-Type: application/json

{
  "storeIds": [1, 2, 3]
}
```

**Response:**
```json
{
  "id": 1,
  "username": "john.doe",
  "code": "U001",
  "firstName": "John",
  "lastName": "Doe",
  "stores": [
    {
      "id": 1,
      "code": "ST001",
      "name": "ร้านค้าสาขา 1"
    }
  ]
}
```

### 3. ลบ Store ออกจาก User
```http
DELETE /user/{id}/stores
Content-Type: application/json

{
  "storeIds": [1, 2]
}
```

### 4. ดึงข้อมูล User พร้อม Store ที่รับผิดชอบ
```http
GET /user/{id}/with-stores
```

### 5. ดึงรายการ User ทั้งหมดพร้อม Store ที่รับผิดชอบ
```http
GET /user/all/with-stores
```

## Store Management APIs

### 1. ดึงรายการ User ที่รับผิดชอบ Store
```http
GET /store/{id}/users
```

**Response:**
```json
[
  {
    "id": 1,
    "username": "john.doe",
    "code": "U001",
    "firstName": "John",
    "lastName": "Doe",
    "phoneNumber": "0812345678",
    "isActive": true
  }
]
```

### 2. มอบหมาย User ให้ Store
```http
POST /store/{id}/users
Content-Type: application/json

{
  "userIds": [1, 2, 3]
}
```

### 3. ลบ User ออกจาก Store
```http
DELETE /store/{id}/users
Content-Type: application/json

{
  "userIds": [1, 2]
}
```

### 4. ดึงข้อมูล Store พร้อม User ที่รับผิดชอบ
```http
GET /store/{id}/with-users
```

### 5. ดึงรายการ Store ทั้งหมดพร้อม User ที่รับผิดชอบ
```http
GET /store/all/with-users
```

## Database Schema

### user_stores Junction Table
```sql
CREATE TABLE user_stores (
  user_id INT NOT NULL,
  store_id INT NOT NULL,
  PRIMARY KEY (user_id, store_id),
  FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
  FOREIGN KEY (store_id) REFERENCES store(id) ON DELETE CASCADE
);
```

## Use Cases

### 1. มอบหมาย User ให้ดูแลหลาย Store
```javascript
// มอบหมาย User ID 1 ให้ดูแล Store 1, 2, 3
POST /user/1/stores
{
  "storeIds": [1, 2, 3]
}
```

### 2. เพิ่ม User หลายคนให้ดูแล Store เดียวกัน
```javascript
// เพิ่ม User 1, 2, 3 ให้ดูแล Store ID 1
POST /store/1/users
{
  "userIds": [1, 2, 3]
}
```

### 3. ตรวจสอบ Store ที่ User รับผิดชอบ
```javascript
// ดู Store ทั้งหมดที่ User ID 1 รับผิดชอบ
GET /user/1/stores
```

### 4. ตรวจสอบ User ที่รับผิดชอบ Store
```javascript
// ดู User ทั้งหมดที่รับผิดชอบ Store ID 1
GET /store/1/users
```

## Error Responses

### 404 Not Found
```json
{
  "statusCode": 404,
  "message": "User not found"
}
```

### 400 Bad Request
```json
{
  "statusCode": 400,
  "message": "Some stores not found"
}
```
