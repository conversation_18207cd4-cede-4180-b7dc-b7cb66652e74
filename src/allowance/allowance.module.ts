import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Allowance } from './entities/allowance.entity';
import { AllowanceController } from './allowance.controller';
import { AllowanceService } from './allowance.service';

@Module({
    imports: [
        TypeOrmModule.forFeature([
            Allowance
        ])
    ],
    controllers: [AllowanceController],
    providers: [AllowanceService]
})
export class AllowanceModule { }
