import { Controller, Get, Post, Body, Param, Put, Delete, HttpCode, HttpStatus, ClassSerializerInterceptor, UseInterceptors } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { ALLOWANCE_PAGINATION_CONFIG, AllowanceService } from './allowance.service';
import { CreateAllowanceDto } from './dto/create-allowance.dto';
import { UpdateAllowanceDto } from './dto/update-allowance.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';


@Controller('allowance')
@ApiTags('สิ่งแปลกปลอม')
@UseInterceptors(ClassSerializerInterceptor)
export class AllowanceController {
    constructor(private readonly allowanceService: AllowanceService) { }

    @Get('datatables')
    @HttpCode(HttpStatus.OK)
    @ApiPaginationQuery(ALLOWANCE_PAGINATION_CONFIG)
    datatables(@Paginate() query: PaginateQuery) {
        return this.allowanceService.datatables(query);
    }

    @Post()
    @ApiOperation({ summary: 'สร้างข้อมูลสิ่งแปลกปลอม' })
    create(@Body() body: CreateAllowanceDto) {
        return this.allowanceService.create(body);
    }

    @Get()
    @ApiOperation({ summary: 'ดูข้อมูลทั้งหมด' })
    findAll() {
        return this.allowanceService.findAll();
    }

    @Get(':id')
    @ApiOperation({ summary: 'ดูข้อมูลตาม ID' })
    findOne(@Param('id') id: string) {
        return this.allowanceService.findOne(+id);
    }

    @Put(':id')
    @ApiOperation({ summary: 'แก้ไข' })
    update(@Param('id') id: string, @Body() body: UpdateAllowanceDto) {
        return this.allowanceService.update(+id, body);
    }

    @Delete(':id')
    @ApiOperation({ summary: 'ลบ' })
    remove(@Param('id') id: string) {
        return this.allowanceService.remove(+id);
    }
}
