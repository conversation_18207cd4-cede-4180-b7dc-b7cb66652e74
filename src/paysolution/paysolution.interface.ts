export interface PaysolutionResponse {
  status: string
  data: PaysolutionData
}
export interface PaysolutionData {
  orderNo: number
  referenceNo: string
  total: number
  orderdatetime: Date
  expiredate: Date
  image: string
}


export interface PaysolutionRequest {
  // merchantID: number
  productDetail: string
  // customerEmail: string
  customerName: string
  total: number
  referenceNo: number
}
