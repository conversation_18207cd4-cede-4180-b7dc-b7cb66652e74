import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateLevelDto } from './dto/create-level.dto';
import { UpdateLevelDto } from './dto/update-level.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Level } from './entities/level.entity';
import { PaginateQuery, Paginated, paginate } from 'nestjs-paginate';

@Injectable()
export class LevelService {
  constructor(
    @InjectRepository(Level)
    private levelRepository: Repository<Level>,
  ) { }

  create(createLevelDto: CreateLevelDto) {
    const level = this.levelRepository.create(createLevelDto);

    return this.levelRepository.save(level);
  }

  findAll() {
    return this.levelRepository.find();
  }

  async findOne(id: number) {
    const level = await this.levelRepository.findOne({
      where: { id, }
    });
    if (!level) throw new NotFoundException("level not found");

    return level;
  }

  async update(id: number, updateCategoryDto: UpdateLevelDto) {
    const level = await this.findById(id);

    if (!level) throw new NotFoundException("level not found");

    return this.levelRepository.update(id, updateCategoryDto);
  }

  async remove(id: number) {
    const level = await this.findById(id);

    if (!level) throw new NotFoundException("level not found");

    await this.levelRepository.softRemove(level);
  }

  findById(id: number) {
    return this.levelRepository.findOneBy({ id });
  }

  async datatables(query: PaginateQuery): Promise<Paginated<Level>> {
    return paginate(query, this.levelRepository, {
      sortableColumns: ['id', 'name'],
      select: ['id', 'name', 'createdAt'],
    });
  }
}
