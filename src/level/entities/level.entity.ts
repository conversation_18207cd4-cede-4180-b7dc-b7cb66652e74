import { ProductLevel } from "../../product/entities/product-level.entity";
import { CustomBaseEntity } from "../../common/entities";
import { Customer } from "../../customer/entities/customer.entity";
import { Column, Entity, OneToMany } from "typeorm";

@Entity()
export class Level extends CustomBaseEntity {
    @Column()
    name: string;

    // @OneToMany(() => Customer, (_) => _.level)
    // customers: Array<Customer>;

    @OneToMany(() => ProductLevel, (_) => _.level)
    productLevels: ProductLevel[];

    constructor(partial?: Partial<Level>) {
        super();
        if (partial) {
            Object.assign(this, partial)
        }
    }
}
