import { Column, Entity } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";

@Entity()
export class Payment extends CustomBaseEntity {
  @Column({type: 'int8', name: 'reference_no', unique: true })
  referenceNo: number;

  @Column()
  provider: string;

  @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
  total: number;

  @Column()
  status: string;

  @Column({ nullable: true })
  expiredate?: Date;

  @Column('text', { nullable: true })
  rawrequest?: string;

  @Column('text', { nullable: true })
  rawreponse?: string;

  constructor(partial?: Partial<Payment>) {
    super();
    if (partial) {
        Object.assign(this, partial)
    }
}
}
