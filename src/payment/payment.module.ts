import { Module } from '@nestjs/common';
import { PaymentService } from './payment.service';
import { PaymentController } from './payment.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Payment } from './entities/payment.entity';
import { PaysolutionModule } from 'src/paysolution/paysolution.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Payment]),
    PaysolutionModule,
  ],
  // controllers: [PaymentController],
  providers: [PaymentService],
  exports: [PaymentService]
})
export class PaymentModule { }
