import { BadRequestException, Injectable } from '@nestjs/common';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { UpdatePaymentDto } from './dto/update-payment.dto';
import { PaysolutionService } from 'src/paysolution/paysolution.service';
import { OrderPayment } from 'src/order/entities/order-payment.entity';
import { Payment } from './entities/payment.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, LessThanOrEqual, Like, MoreThanOrEqual, Repository } from 'typeorm';
import { Helper } from 'src/common/helper';
import { lastValueFrom } from 'rxjs';

@Injectable()
export class PaymentService {
  constructor(
    private paysolutionService: PaysolutionService,
    @InjectRepository(Payment)
    private paymentRepository: Repository<Payment>,
  ) { }

  async createPayment(orderPayment: OrderPayment) {
    // get last payment number of day
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const startOfDay = new Date(today);
    const endOfDay = new Date(today);
    endOfDay.setDate(endOfDay.getDate() + 1);

    const lastPayment = await this.paymentRepository.findOne({
      where: [
        { createdAt: Between(startOfDay, endOfDay)},
      ],
      order: { createdAt: 'DESC' }
    });

    const payment = new Payment();
    if (!lastPayment) {
      payment.referenceNo = Helper.generateNumber(null);
      payment.provider = orderPayment.paymentMethod.type;
      payment.total = orderPayment.amount;
      payment.status = 'created'
    } else {
      payment.referenceNo = +lastPayment.referenceNo + 1;
      payment.provider = orderPayment.paymentMethod.type;
      payment.total = orderPayment.amount;
      payment.status = 'created'
    }
    
    let image = null;

    try {
      const resp = await lastValueFrom(this.paysolutionService.createThaiQR('กาแฟ', 'upos', payment.total, payment.referenceNo));
      payment.rawrequest = JSON.stringify(resp.config);
      payment.rawreponse = JSON.stringify(resp.data);
      payment.expiredate = resp.data.data.expiredate;
      image = resp.data.data.image;
      await this.paymentRepository.save(payment);
    } catch (error) {
      console.error(error);
      throw new BadRequestException(error);
    }

    return {
      total: payment.total,
      expiredate: payment.expiredate,
      image: image,
    }
  }

}
