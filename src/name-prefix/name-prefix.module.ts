import { Modu<PERSON> } from '@nestjs/common';
import { NamePrefixService } from './name-prefix.service';
import { NamePrefixController } from './name-prefix.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NamePrefix } from './entities/name-prefix.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      NamePrefix
    ])
  ],
  controllers: [NamePrefixController],
  providers: [NamePrefixService],
})
export class NamePrefixModule {}
