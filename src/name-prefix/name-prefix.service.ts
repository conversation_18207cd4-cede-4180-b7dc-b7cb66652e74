import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateNamePrefixDto } from './dto/create-name-prefix.dto';
import { UpdateNamePrefixDto } from './dto/update-name-prefix.dto';
import { NamePrefix } from './entities/name-prefix.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';

export const PREFIX_PAGINATION_CONFIG: PaginateConfig<NamePrefix> = {
  sortableColumns: ['id', 'name'],
  searchableColumns: ['name']
};

@Injectable()
export class NamePrefixService {
  constructor(
    @InjectRepository(NamePrefix)
    private namePrefixRepository: Repository<NamePrefix>,
  ) { }

  create(createNamePrefixDto: CreateNamePrefixDto) {
    return this.namePrefixRepository.save(createNamePrefixDto);
  }

  findAll() {
    return this.namePrefixRepository.find()
  }

  async findOne(id: number) {
    const prefix = await this.namePrefixRepository.findOneBy({ id })

    if (!prefix) {
      throw new NotFoundException('Prefix not found')
    }

    return prefix
  }

  async update(id: number, updateNamePrefixDto: UpdateNamePrefixDto) {
    const prefix = await this.namePrefixRepository.findOneBy({ id })

    if (!prefix) {
      throw new NotFoundException('Prefix not found')
    }

    prefix.name = updateNamePrefixDto.name

    await prefix.save()

    return prefix
  }

  async remove(id: number) {
    const prefix = await this.namePrefixRepository.findOneBy({ id })

    if (!prefix) {
      throw new NotFoundException('Prefix not found')
    }

    await this.namePrefixRepository.remove(prefix)
  }

  async datatables(query: PaginateQuery): Promise<Paginated<NamePrefix>> {
    return paginate(query, this.namePrefixRepository, PREFIX_PAGINATION_CONFIG);
  }
}
