import { Controller, Get, Post, Body, Param, Delete, HttpCode, HttpStatus, Put } from '@nestjs/common';
import { NamePrefixService, PREFIX_PAGINATION_CONFIG } from './name-prefix.service';
import { CreateNamePrefixDto } from './dto/create-name-prefix.dto';
import { UpdateNamePrefixDto } from './dto/update-name-prefix.dto';
import { ApiTags } from '@nestjs/swagger';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';

@Controller('name-prefix')
@ApiTags('คำนำหน้าชื่อ')
export class NamePrefixController {
  constructor(private readonly namePrefixService: NamePrefixService) {}

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(PREFIX_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.namePrefixService.datatables(query);
  }

  @Post()
  create(@Body() createNamePrefixDto: CreateNamePrefixDto) {
    return this.namePrefixService.create(createNamePrefixDto);
  }

  @Get()
  findAll() {
    return this.namePrefixService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.namePrefixService.findOne(+id);
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() updateNamePrefixDto: UpdateNamePrefixDto) {
    return this.namePrefixService.update(+id, updateNamePrefixDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.namePrefixService.remove(+id);
  }
}
