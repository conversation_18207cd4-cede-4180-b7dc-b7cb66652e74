import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateAttributeDto } from './dto/create-attribute.dto';
import { UpdateAttributeDto } from './dto/update-attribute.dto';
import { PaginateQuery, Paginated, paginate } from 'nestjs-paginate';
import { Attribute } from './entities/attribute.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class AttributeService {
  constructor(
    @InjectRepository(Attribute)
    private attributeRepository: Repository<Attribute>,
  ) { }

  create(createAttributeDto: CreateAttributeDto) {
    const attribute = this.attributeRepository.create(createAttributeDto);

    return this.attributeRepository.save(attribute);
  }

  findAll() {
    return this.attributeRepository.find();
  }

  async findOne(id: number) {
    const attribute = await this.attributeRepository.findOne({
      where: {
        id,
      }
    });

    if (!attribute) {
      throw new NotFoundException(`attribute ${id} not found`);
    }

    return attribute;
  }

  update(id: number, updateAttributeDto: UpdateAttributeDto) {
    return this.attributeRepository.update(id, {
      ...updateAttributeDto,
    });
  }

  async remove(id: number) {
    await this.attributeRepository.delete(id);
  }

  async datatables(query: PaginateQuery): Promise<Paginated<Attribute>> {
    return paginate(query, this.attributeRepository, {
      sortableColumns: ['id', 'name'],
      // relations: {  },
      // nullSort: 'last',
      // defaultSortBy: [['id', 'DESC']],
      // searchableColumns: ['name', 'color', 'age'],
      select: ['id', 'name', 'createdAt'],
      // filterableColumns: {
      //   name: [FilterOperator.EQ, FilterSuffix.NOT],
      //   age: true,
      // },
    });
  }
}
