import { Module } from '@nestjs/common';
import { QueueService } from './queue.service';
import { QueueController } from './queue.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Queue } from './entities/queue.entity';
import { Branch } from '../branch/entities/branch.entity';

@Module({
    imports: [
        TypeOrmModule.forFeature([Queue, Branch]),
    ],
    controllers: [QueueController],
    providers: [QueueService],
})
export class QueueModule { }
