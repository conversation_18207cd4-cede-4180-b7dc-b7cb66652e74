import { PartialType } from '@nestjs/swagger';
import { CreateQueueDto } from './create-queue.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, Length, IsArray, IsEnum } from 'class-validator';
import { CreateQueuePhotoDto } from './create-queue-photo.dto';
import { PurposeStatus, QueueStatus } from '../entities/queue.entity';
import { CarType } from '../entities/car-type.entity';

export class UpdateQueueDto extends PartialType(CreateQueueDto) {
    @ApiProperty({ example: '1234567890098' })
    @IsOptional()
    @Length(13)
    readonly idCard?: string;

    @ApiProperty({ example: 'แดง' })
    @IsOptional()
    readonly customerName?: string;

    @ApiProperty({ example: '0812345678' })
    @IsOptional()
    readonly phoneNumber?: string;

    @ApiProperty({ example: 'กข 1234' })
    @IsOptional()
    readonly vehicleNumber?: string;

    // @ApiProperty({ example: ['q1_1.jpg', 'q1_2.jpg'] })
    // @IsOptional()
    // readonly photos?: string[];

    @ApiProperty({ type: [CreateQueuePhotoDto], example: [{ path: 'q1_1.jpg', status: 'income' }] })
    @IsOptional()
    @IsArray()
    readonly photos?: CreateQueuePhotoDto[];

    @ApiProperty({ example: 'AshaTech' })
    @IsOptional()
    readonly customerCompany?: string;

    @ApiProperty({ example: 'ฝ่ายซื้อ' })
    @IsOptional()
    readonly department?: string;

    @ApiProperty({ example: 6 })
    readonly carTypeId?: number;

    @ApiProperty({ example: 1 })
    @IsOptional()
    readonly branchId?: number;

    @ApiProperty({ example: '1' })
    @IsOptional()
    readonly visitorNo?: number;

    @ApiProperty({ example: 250 })
    @IsOptional()
    readonly weight1?: number;

    @ApiProperty({ example: '12:17:31' })
    @IsOptional()
    readonly weight1Time?: string;

    @ApiProperty({ example: 248 })
    @IsOptional()
    readonly weight2?: number;

    @ApiProperty({ example: '12:18:25' })
    @IsOptional()
    readonly weight2Time?: string;

    @ApiProperty({
        example: PurposeStatus.SEND,
        enum: PurposeStatus
    })
    @IsEnum(PurposeStatus)
    @IsOptional()
    readonly purpose?: PurposeStatus;

    @ApiProperty({ example: 'เบิกออก' })
    @IsOptional()
    readonly otherPurposeDetail?: string;

    @ApiProperty({ example: 'ประเภทสินค้า' })
    @IsOptional()
    readonly productType?: string;

    @ApiProperty({ example: '15:19:59' })
    @IsOptional()
    readonly doneTime?: string;

    @ApiProperty({
        example: QueueStatus.DONE,
        enum: QueueStatus
    })
    @IsEnum(QueueStatus)
    @IsOptional()
    readonly status?: QueueStatus;

}
