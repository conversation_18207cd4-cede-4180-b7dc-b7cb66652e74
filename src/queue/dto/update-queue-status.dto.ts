import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsString, IsArray } from 'class-validator';
import { CreateQueuePhotoDto } from './create-queue-photo.dto';

export class UpdateQueueStatusDto {
    @ApiProperty({ example: 16.8 })
    @IsOptional()
    @IsNumber()
    readonly weight1?: number;

    @ApiProperty({ example: '12:17:31' })
    @IsOptional()
    @IsString()
    readonly weight1Time?: string;

    @ApiProperty({ example: 18.7 })
    @IsOptional()
    @IsNumber()
    readonly weight2?: number;

    @ApiProperty({ example: '12:20:25' })
    @IsOptional()
    @IsString()
    readonly weight2Time?: string;

    @ApiProperty({ type: [CreateQueuePhotoDto], example: [{ path: 'q1_1.jpg' }] })
    @IsOptional()
    @IsArray()
    readonly photos?: CreateQueuePhotoDto[];
}
