import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsEnum, IsOptional } from "class-validator";
import { QueuePhotoStatus } from "../entities/queue-photo.entity";

export class CreateQueuePhotoDto {
    @ApiProperty({ example: 1, description: 'ID ของ upload' })
    readonly uploadId: number;

    @ApiProperty({
        example: QueuePhotoStatus.INCOME,
        enum: QueuePhotoStatus,
    })
    @IsOptional()
    @IsEnum(QueuePhotoStatus)
    readonly status: QueuePhotoStatus;
}
