import { Column, Entity, ManyToOne } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { QueueItem } from "./queue-item.entity";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";

@Entity()
export class QueueItemAllowance extends CustomBaseEntity {
    @Column()
    name: string;

    @Column('numeric', { nullable: true, transformer: new DecimalColumnTransformer() })
    weight: number;

    @ManyToOne(() => QueueItem, (_) => _.allowance, { onDelete: 'CASCADE' })
    queueItem: QueueItem;

}