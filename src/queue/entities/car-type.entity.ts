import { Column, Entity, OneToMany } from 'typeorm';
import { CustomBaseEntity } from '../../common/entities';
import { Queue } from '../../queue/entities/queue.entity';

@Entity()
export class CarType extends CustomBaseEntity {
    @Column({ unique: true, nullable: true })
    code: string;

    @Column()
    name: string;

    @Column()
    type: string;

    @OneToMany(() => Queue, (_) => _.carType)
    queues: Queue[];
}
