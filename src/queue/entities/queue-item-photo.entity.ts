import { Column, Entity, ManyToOne } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Queue } from "./queue.entity";
import { Expose } from "class-transformer";
import { QueueItem } from "./queue-item.entity";
import { Upload } from "src/upload/entities/upload.entity";

export enum QueueItemPhotoStatus {
    GROSS_WEIGHT = 'gross_weight',
    TARE_WEIGHT = 'tare_weight',
}

@Entity()
export class QueueItemPhoto extends CustomBaseEntity {
    @ManyToOne(() => Upload, (_) => _.queueItemPhotos)
    upload: Upload;

    @ManyToOne(() => QueueItem, (queue) => queue.queueItemWeight, { onDelete: 'CASCADE' })
    queueItem: QueueItem;

    @Column()
    weight: number;

    @Column({ type: 'enum', enum: QueueItemPhotoStatus, default: QueueItemPhotoStatus.GROSS_WEIGHT, nullable: true })
    status: QueueItemPhotoStatus;
}