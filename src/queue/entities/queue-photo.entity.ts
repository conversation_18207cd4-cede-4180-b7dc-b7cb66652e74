import { Column, Entity, ManyToOne } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Queue } from "./queue.entity";
import { Expose } from "class-transformer";
import { Upload } from "src/upload/entities/upload.entity";

export enum QueuePhotoStatus {
    INCOME = 'income',
    WEIGHT = 'weight',
    SORT = 'sort',
    DONE = 'done',
}


@Entity()
export class QueuePhoto extends CustomBaseEntity {
    @ManyToOne(() => Upload, (_) => _.queuePhotos)
    upload: Upload;

    @Column({ nullable: true })
    type: string;

    @Column({ type: 'enum', enum: QueuePhotoStatus, default: QueuePhotoStatus.INCOME })
    status: QueuePhotoStatus;

    @ManyToOne(() => Queue, (queue) => queue.photos, { onDelete: 'CASCADE' })
    queue: Queue;
}