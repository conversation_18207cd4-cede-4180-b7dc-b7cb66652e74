import { Controller, Get, Post, Body, Param, Put, Delete, HttpCode, HttpStatus, Req, Query, ClassSerializerInterceptor, UseInterceptors, ParseArrayPipe } from '@nestjs/common';
import { QUEUE_PAGINATION_CONFIG, QueueService } from './queue.service';
import { CreateQueueDto } from './dto/create-queue.dto';
import { CreateQueueItemArrayDto } from './dto/create-queue-item.dto';
import { UpdateQueueDto } from './dto/update-queue.dto';
import { ApiTags, ApiOperation, ApiBody, ApiQuery } from '@nestjs/swagger';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { UpdateQueueStatusDto } from './dto/update-queue-status.dto';
import { SkipQueueDto } from './dto/skip-queue-note.dto';
import { QueueStatus } from './entities/queue.entity';
import { OutQueueDto } from './dto/out-queue.dto';

@Controller('queue')
@ApiTags('คิว')
@Auth()
@UseInterceptors(ClassSerializerInterceptor)
export class QueueController {
    constructor(private readonly queueService: QueueService, private readonly carTypeService: QueueService) { }
    // constructor(private readonly carTypeService: QueueService) { }

    @Get('datatables')
    @HttpCode(HttpStatus.OK)
    @ApiPaginationQuery(QUEUE_PAGINATION_CONFIG)
    datatables(@Paginate() query: PaginateQuery) {
        return this.queueService.datatables(query);
    }

    @Post()
    @ApiOperation({ summary: 'สร้างคิว' })
    create(@Body() body: CreateQueueDto, @Req() req) {
        const userId = req.user['sub'];
        return this.queueService.createQueue(body, userId);
    }

    @Get()
    @ApiOperation({ summary: 'แสดงคิวทั้งหมดแยกตามสถานะ' })
    @ApiQuery({ required: false, allowEmptyValue: true, name: 'status', type: [String], enum: QueueStatus, isArray: true })
    @ApiQuery({ required: false, allowEmptyValue: true, name: 'queueDate', type: String, example: '2025-05-21' })
    getAllQueue(
        @Query('status', new ParseArrayPipe({ items: String, optional: true })) status?: QueueStatus[],
        @Query('queueDate') queueDate?: string) {
        return this.queueService.getAllQueue(status, queueDate);
    }

    @Get('missing')
    @ApiOperation({ summary: 'แสดงคิวที่หาย ทั้งหมดแยกตามสถานะ' })
    @ApiQuery({ required: false, allowEmptyValue: true, name: 'status', enum: QueueStatus })
    @ApiQuery({ required: false, allowEmptyValue: true, name: 'queueDate', type: String, example: '2025-05-21' })
    getAllQueueMissing(@Query('status') status?: QueueStatus, @Query('queueDate') queueDate?: string) {
        return this.queueService.getAllQueueMissing(status, queueDate);
    }

    @Get('cartypes')
    @ApiOperation({ summary: 'แสดง cartype ทั้งหมด' })
    getAllCarType() {
        return this.queueService.getAllCarType();
    }

    @Get('run-queue-number')
    @ApiOperation({ summary: 'รันหมายเลขคิว' })
    runQueueNumebr() {
        return this.queueService.runQueueNumber();
    }

    @Get(':id')
    @ApiOperation({ summary: 'แสดงคิวตาม id' })
    findOneQueue(@Param('id') id: string) {
        return this.queueService.findOneQueue(+id);
    }

    @Put(':id')
    @ApiOperation({ summary: 'แก้ไขคิว' })
    updateQueue(@Param('id') id: string, @Body() updateQueue: UpdateQueueDto) {
        return this.queueService.updateQueue(+id, updateQueue);
    }

    @Put(':id/status')
    @ApiOperation({ summary: 'กรอกน้ำหนัก' })
    updateQueueStatusToWeighing(@Param('id') id: string, @Body() updateQueueStatus: UpdateQueueStatusDto, @Req() req) {
        const userId = req.user['sub'];
        return this.queueService.updateQueueStatusToWeighing(+id, updateQueueStatus, userId);
    }

    @Post(':id/skipped')
    @ApiOperation({ summary: 'ข้ามคิว id นั้น' })
    skipQueue(@Param('id') id: string, @Body() skipQueue: SkipQueueDto) {
        return this.queueService.skipQueue(+id, skipQueue);
    }

    @Delete(':id')
    @ApiOperation({ summary: 'ลบคิว' })
    remove(@Param('id') id: string) {
        return this.queueService.remove(+id);
    }

    @Post(':id/queueItems')
    @ApiOperation({
        summary: 'เพิ่ม queueItems',
        description: `
        * status เมื่อต้องการแค่บันทึกข้อมูลให้ส่ง status = weighed
        * status เมื่อต้องการเปลี่ยนสถานะให้ส่ง status = sorted
    `
    })
    @ApiBody({ type: CreateQueueItemArrayDto })
    addQueueItems(@Param('id') id: string, @Body() createQueueItemArrayDto: CreateQueueItemArrayDto) {
        return this.queueService.addQueueItems(+id, createQueueItemArrayDto);
    }

    @Post(':id/outQueue')
    @ApiOperation({ summary: 'ออกคิว' })
    outQueue(@Param('id') id: string, @Body() outQueueDto: OutQueueDto) {
        return this.queueService.outQueue(+id, outQueueDto);
    }

    @Post('call-next')
    callNext() {
        return this.queueService.callNextQueue();
    }

    @Put(':id/finish')
    finish(@Param('id') id: number) {
        return this.queueService.finishQueue(+id);
    }

    @Put(':id/cancel')
    cancel(@Param('id') id: number) {
        return this.queueService.cancelQueue(+id);
    }

}
