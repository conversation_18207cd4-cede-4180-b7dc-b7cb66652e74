import { <PERSON>du<PERSON> } from '@nestjs/common';
import { RewardService } from './reward.service';
import { RewardController } from './reward.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Reward } from './entities/reward.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import { RewardReport } from './entities/rewardreport.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([<PERSON><PERSON>,Customer,RewardReport])
  ],
  controllers: [RewardController],
  providers: [RewardService],
})
export class RewardModule {}
