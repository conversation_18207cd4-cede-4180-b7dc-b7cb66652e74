import { isNotEmpty, IsNot<PERSON>mpt<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "class-validator";

export class CreateRewardDto {
  @IsNotEmpty()
  readonly code: string;

  @IsNotEmpty()
  readonly name: string;

  @IsNotEmpty()
  readonly detail: string;

  @IsNotEmpty()
  @IsNumber()
  readonly amount: number;

  @IsNotEmpty()
  readonly startDate: Date;

  @IsNotEmpty()
  readonly endDate: Date;

  @IsNotEmpty()
  readonly isActive: boolean;
}

export class PaidDto {
  @IsNotEmpty()
  readonly amount: number;

  @IsNotEmpty()
  readonly customer_id: number;

  @IsNotEmpty()
  readonly point_balance: number;

  @IsNotEmpty()
  readonly reward_id: number;

  @IsNotEmpty()
  @IsNumber()
  readonly qty: number;






}
