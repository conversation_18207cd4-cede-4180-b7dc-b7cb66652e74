import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateRewardDto } from './dto/create-reward.dto';
import { UpdatePromotionDto } from './dto/update-reward.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Reward } from './entities/reward.entity';
import { Repository } from 'typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';
import { Customer } from 'src/customer/entities/customer.entity';
import { notContains } from 'class-validator';
import { RewardReport } from './entities/rewardreport.entity';

export const PROMOTION_PAGINATION_CONFIG: PaginateConfig<Reward> = {
  sortableColumns: ['id', 'code', 'name'],
  searchableColumns: ['code', 'name'],
  filterableColumns: {
    type: true,
  },
  relativePath: true,
};
@Injectable()
export class RewardService {
  constructor(
    @InjectRepository(Reward)
    private rewardRepository: Repository<Reward>,
    @InjectRepository(Customer)
    private customerRepository: Repository<Customer>,
    @InjectRepository(RewardReport)
    private rewardReportRepository: Repository<RewardReport>
  ) { }
  create(createRewardDto: CreateRewardDto) {

    const promotion = this.rewardRepository.create(createRewardDto);

    return this.rewardRepository.save(promotion);
  }

  findAll() {
    return this.rewardRepository.find();
  }

  async findOne(id: number) {
    const promotion = await this.rewardRepository.findOneBy({ id })
    if (!promotion) throw new NotFoundException("promotion not found");

    return promotion;
  }

  async update(id: number, updatePromotionDto: UpdatePromotionDto) {
    const promotion = await this.rewardRepository.findOneBy({ id })
    if (!promotion) throw new NotFoundException("promotion not found");

    return this.rewardRepository.update(id, updatePromotionDto);
  }

  async remove(id: number) {
    const reward = await this.rewardRepository.findOneBy({ id })
    if (!reward) throw new NotFoundException("reward not found");

    await this.rewardRepository.softRemove(reward);
  }

  async datatables(query: PaginateQuery): Promise<Paginated<Reward>> {
    return paginate(query, this.rewardRepository, PROMOTION_PAGINATION_CONFIG);
  }



  async paidreward(amount: number, qty: number, customer_id: number, point_balance: number, reward_id: number,) {
    const customer = await this.customerRepository.findOneBy({ id: customer_id })
    if (!customer) {
      throw new NotFoundException("customer not found")
    }

    const reward = await this.rewardRepository.findOneBy({ id: reward_id })
    if (!reward) {

      throw new NotFoundException("reward not found")
    }

    let currentpoint = customer.pointBalance
    const cal = currentpoint - amount
    if (cal < 0) {
      throw new BadRequestException("your point is too low")
    }

    await this.customerRepository.update(customer_id, { pointBalance: cal });

    const rewardReport = new RewardReport();
    rewardReport.reward_name = reward.name
    rewardReport.reward = reward
    rewardReport.customer_name = customer.name
    rewardReport.customer = customer
    rewardReport.amout = -amount
    rewardReport.qty = qty
    rewardReport.point_balance = point_balance
    await this.rewardReportRepository.save(rewardReport);

    return { reward, customer, rewardReport }

  }
}
