import { Controller, Get, Post, Body, Param, Delete, HttpCode, HttpStatus, Put } from '@nestjs/common';
import { PROMOTION_PAGINATION_CONFIG, RewardService } from './reward.service';
import { CreateRewardDto, PaidDto } from './dto/create-reward.dto';
import { UpdatePromotionDto } from './dto/update-reward.dto';
import { ApiConsumes, ApiTags } from '@nestjs/swagger';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { Auth } from 'src/auth/decorators/auth.decorator';

@Controller('reward')
@ApiTags('แลกของ')
@Auth()
export class RewardController {
  constructor(private readonly rewardService: RewardService) { }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(PROMOTION_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.rewardService.datatables(query);
  }

  @Post('paid')
  @ApiConsumes('application/x-www-form-urlencoded')
  @ApiConsumes('application/json')
  paidreward(@Body() paidDto: PaidDto) {
    const { amount, qty, customer_id, point_balance, reward_id } = paidDto;
    return this.rewardService.paidreward(amount, qty, customer_id, point_balance, reward_id,);
  }

  @Post()
  @ApiConsumes('application/x-www-form-urlencoded')
  @ApiConsumes('application/json')
  create(@Body() createRewardDto: CreateRewardDto) {
    return this.rewardService.create(createRewardDto);
  }


  @Get()
  findAll() {
    return this.rewardService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.rewardService.findOne(+id);
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() updatePromotionDto: UpdatePromotionDto) {
    return this.rewardService.update(+id, updatePromotionDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.rewardService.remove(+id);
  }
}
