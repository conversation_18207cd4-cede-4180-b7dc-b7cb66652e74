import { CustomBaseEntity } from "../../common/entities";
import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from "typeorm";
import { Customer } from "../../customer/entities/customer.entity";

@Entity()
export class Reward extends CustomBaseEntity {
  @Column({ unique: true })
  code: string;

  @Column()
  name: string;

  @Column()
  detail: string;

  @Column()
  amount: number;

  @Column({ name: 'start_date', type: 'date' })
  startDate: Date;

  @Column({ name: 'end_date', type: 'date' })
  endDate: Date;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;
}
