import { CustomBaseEntity } from "../../common/entities";
import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from "typeorm";
import { Customer } from "../../customer/entities/customer.entity";
import { Reward } from "./reward.entity";
import { collapseTextChangeRangesAcrossMultipleVersions } from "typescript";

@Entity()
export class RewardReport extends CustomBaseEntity {
  @Column()
  reward_name:string

  @ManyToOne(() => Reward)
  @JoinColumn({ name: 'reward_id' }) 
  reward: Reward;  

  @Column()
  customer_name:string

  @ManyToOne(() => Customer)
  @JoinColumn({name:'customer_id'})
  customer: Customer;

  @Column()
  amout:number

  @Column({ nullable: true})
  qty:number

  @Column()
  point_balance:number
}
