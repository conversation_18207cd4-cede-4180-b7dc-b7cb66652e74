import * as argon2 from 'argon2';

import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { Between, Not, Repository, In } from 'typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';
import { Order, OrderStatus } from 'src/order/entities/order.entity';
import { ForceChangePasswordDto } from './dto/force-change-password.dto';
import { AssignStoresDto } from './dto/assign-stores.dto';
import { Store } from '../store/entities/store.entity';

export const USER_PAGINATION_CONFIG: PaginateConfig<User> = {
  sortableColumns: ['id', 'code', 'username', 'phoneNumber'],
  searchableColumns: ['code', 'username', 'firstName', 'lastName', 'phoneNumber'],
  relations: ['role'],
  select: ['id', 'username', 'code', 'firstName', 'lastName', 'phoneNumber',
    'role.id', 'role.name', 'isActive', 'createdAt'],
};

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Store)
    private storeRepository: Repository<Store>,
  ) { }

  async create(createUserDto: CreateUserDto) {
    const usernameIsExist = await this.userRepository.existsBy({ username: createUserDto.username });
    if (usernameIsExist) throw new BadRequestException('username already exists');

    const codeIsExist = await this.userRepository.existsBy({ code: createUserDto.code });
    if (codeIsExist) throw new BadRequestException('code already exists');

    const hash = await argon2.hash(createUserDto.password);

    const user = this.userRepository.create({
      ...createUserDto,
      password: hash,
      isActive: true,
      role: { id: createUserDto.roleId },
    });

    return this.userRepository.save(user);
  }

  findAll() {
    return this.userRepository.find();
  }

  async findOne(id: number) {
    const user = await this.userRepository.findOne({
      where: { id },
      select: {
        role: {
          id: true,
          name: true,
          permissions: {
            id: true,
            name: true
          }
        }
      },
      relations: {
        role: {
          permissions: true
        }
      },
    });

    if (!user) throw new NotFoundException("user not found");

    return user;
  }

  async update(id: number, updateUserDto: UpdateUserDto) {
    const user = await this.findById(id)
    if (!user) throw new NotFoundException("user not found");

    // const codeIsExist = await this.userRepository.exists({
    //   where: { id: Not(id), code: updateUserDto.code, }
    // });
    // if (codeIsExist) throw new BadRequestException("code already exists");
    const userData = this.userRepository.create({
      ...updateUserDto,
      role: {
        id: updateUserDto?.roleId,
      }
    })

    return this.userRepository.update(id, userData);
  }

  async remove(id: number) {
    const user = await this.findById(id)
    if (!user) throw new NotFoundException("user not found");

    const date = new Date().valueOf();

    await this.userRepository.update(id, { code: `${date}-${user.code}`, username: `${date}-${user.username}` });

    await this.userRepository.softDelete(id);
  }

  findByUsername(username: string) {
    return this.userRepository.findOneBy({ username });
  }

  findById(userId: number) {
    return this.userRepository.findOneBy({ id: userId });
  }

  async datatables(query: PaginateQuery): Promise<Paginated<User>> {
    return paginate(query, this.userRepository, USER_PAGINATION_CONFIG);
  }

  async updateRefreshToken(userId: number, token: string) {
    await this.userRepository.update(userId, {
      refreshToken: token,
    });
  }

  async orders(userId: number, start: Date, end: Date, branchId: number) {
    const orders = await Order.find({
      select: {
        id: true, orderDate: true, orderNo: true, grandTotal: true,
        user: {
          id: true,
          firstName: true, lastName: true,
        },
        paymentMethod: {
          id: true,
          name: true,
        },
        branch: {
          id: true, name: true
        }
      },
      where: {
        user: { id: userId },
        orderDate: Between(start, end),
        branch: { id: branchId },
        orderStatus: OrderStatus.SELECT_PAYMENT,
      },
      relations: {
        user: true,
        paymentMethod: true,
        branch: true,
      }
    });

    return {
      totalOrder: orders.length,
      totalPrice: orders.reduce((total, order) => total + order.grandTotal, 0),
      orders
    }
  }

  async forceChangePassword(id: number, password: string) {
    const usernameIsExist = await this.userRepository.existsBy({ id });
    if (!usernameIsExist) {
      throw new BadRequestException('user not found');
    }

    const hash = await argon2.hash(password);

    await this.userRepository.update({ id}, { password: hash });

    return {
      message: 'password has been changed'
    }
  }

  async assignStores(userId: number, assignStoresDto: AssignStoresDto) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['stores']
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // ตรวจสอบว่า stores ที่ระบุมีอยู่จริง
    const stores = await this.storeRepository.find({
      where: { id: In(assignStoresDto.storeIds) }
    });

    if (stores.length !== assignStoresDto.storeIds.length) {
      throw new BadRequestException('Some stores not found');
    }

    // กำหนด stores ให้ user
    user.stores = stores;
    await this.userRepository.save(user);

    return this.findOneWithStores(userId);
  }

  async removeStores(userId: number, storeIds: number[]) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['stores']
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // ลบ stores ที่ระบุออกจาก user
    user.stores = user.stores.filter(store => !storeIds.includes(store.id));
    await this.userRepository.save(user);

    return this.findOneWithStores(userId);
  }

  async getUserStores(userId: number) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['stores']
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user.stores;
  }

  async findOneWithStores(id: number) {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['role', 'stores']
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async findAllWithStores() {
    return this.userRepository.find({
      relations: ['role', 'stores'],
      select: {
        id: true,
        username: true,
        code: true,
        firstName: true,
        lastName: true,
        phoneNumber: true,
        isActive: true,
        createdAt: true,
        role: {
          id: true,
          name: true
        },
        stores: {
          id: true,
          code: true,
          name: true
        }
      }
    });
  }
}
