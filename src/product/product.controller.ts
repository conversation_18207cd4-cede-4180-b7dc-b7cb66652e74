import { Body, ClassSerializerInterceptor, Controller, Delete, Get, Header, HttpCode, HttpStatus, Param, ParseIntPipe, Post, Put, Query, Res, StreamableFile, UseInterceptors } from '@nestjs/common';
import { ApiQuery, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { Readable } from 'stream';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { ProductReportService } from './product-report.service';
import { PRODUCT_PAGINATION_CONFIG, ProductService } from './product.service';


@Controller('product')
@ApiTags('สินค้า')
@Auth()
@UseInterceptors(ClassSerializerInterceptor)
export class ProductController {
    constructor(
        private readonly productService: ProductService,
        private readonly productReportService: ProductReportService,

    ) { }

    @Post('exports')
    @Header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    @Header('Content-Disposition', 'attachment; filename="export-product.xlsx"')
    async exportProducts() {
        const content: any = await this.productReportService.exportAll();

        const file = Readable.from(content);

        // res.set({
        //   'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        //   'Content-Disposition': `attachment; filename="report-order.xlsx"`,
        // });

        return new StreamableFile(file);
    }

    @Get('datatables')
    @HttpCode(HttpStatus.OK)
    @ApiPaginationQuery(PRODUCT_PAGINATION_CONFIG)
    datatables(@Paginate() query: PaginateQuery) {
        return this.productService.datatables(query);
    }

    // @Post('/:id/attribute')
    // async createAttribute(@Param('id', ParseIntPipe) id: string, @Body() ceateProductAttributeDto: CreateProductAttributeDto) {
    //   return this.productService.createAttribute(+id, ceateProductAttributeDto);
    // }

    @Delete('attribute/:productAttributeId')
    async deleteAttribute(
        @Param('productAttributeId', ParseIntPipe) attributeId: string) {
        await this.productService.deleteAttribute(+attributeId);
    }

    @Post()
    create(@Body() createProductDto: CreateProductDto) {
        return this.productService.createProductLevel(createProductDto);
    }

    @Get()
    @ApiQuery({ name: 'categoryId', required: false })
    @ApiQuery({ name: 'levelId', required: false })
    findAll(@Query('categoryId') categoryId: string, @Query('levelId') levelId: string) {
        return this.productService.findAll({ categoryId: +categoryId, levelId: +levelId });
    }

    @Get(':id')
    findOne(@Param('id') id: string) {
        return this.productService.findOne(+id);
    }

    @Get('history/:id')
    purchaseHistory(@Param('id') id: string) {
        return this.productService.purchaseHistory(+id);
    }

    @Put(':id')
    update(@Param('id') id: string, @Body() updateProductDto: UpdateProductDto) {
        return this.productService.update(+id, updateProductDto);
    }

    @Delete(':id')
    remove(@Param('id') id: string) {
        return this.productService.remove(+id);
    }
}
