import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateProductDto, Type } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Product } from './entities/product.entity';
import { FindOptionsWhere, In, Repository } from 'typeorm';
import {
    FilterOperator,
    PaginateConfig,
    PaginateQuery,
    Paginated,
    paginate,
} from 'nestjs-paginate';
import { CreateProductAttributeDto } from './dto/create-product-attribute.dto';
import { ProductAttribute } from './entities/product-attribute.entity';
import { ProductAttributeValue } from './entities/product-attribute-value.entity';
import { ConfigService } from '@nestjs/config';
import { imagePath } from 'src/common/helper';
import { ProductRepository } from './product.repository';
import { QueueItem } from 'src/queue/entities/queue-item.entity';
import { DateTime } from 'luxon';

export const PRODUCT_PAGINATION_CONFIG: PaginateConfig<Product> = {
    sortableColumns: [
        'id',
        'code',
        'name',
        'price',
        'category.name',
        'unit.name',
        'branch.name'
    ],
    relations: {
        branch: true,
        category: true,
        unit: true,
        image: true,
        productLevels: { level: true },
    },
    defaultSortBy: [['code', 'ASC']],
    searchableColumns: ['code', 'name', 'branch.name'],
    filterableColumns: {
        'category.id': [FilterOperator.EQ],
        'productLevels.level.id': [FilterOperator.EQ],
        'branch.id': [FilterOperator.EQ],
    },
};

@Injectable()
export class ProductService {
    constructor(
        private productRepository: ProductRepository,
    ) { }

    async create(createProductDto: CreateProductDto) {
        const product = this.productRepository.repository.create({
            ...createProductDto,
            category: {
                id: createProductDto.categoryId,
            },
            unit: {
                id: createProductDto.unitId,
            },
        });

        await this.productRepository.repository.save(product);

        return this.findOne(product.id);
    }

    async findAll(filter: { categoryId: number; levelId: number }) {
        let where: FindOptionsWhere<Product> = {};

        if (filter?.categoryId) {
            where = {
                ...where,
                category: {
                    id: filter.categoryId,
                },
            };
        }

        if (filter?.levelId) {
            where = {
                ...where,
                productLevels: {
                    level: {
                        id: filter.levelId,
                    },
                },
            };
        }

        const products = await this.productRepository.repository.find({
            where: where,
            relations: {
                category: true,
                unit: true,
                image: true,
                productLevels: {
                    level: true,
                },
            },
            order: {
                code: 'ASC',
            },
        });

        for (const product of products) {
            if (product.productLevels.length) {
                product.price = product.productLevels[0].price;
            }
        }

        return products;
    }

    async findOne(id: number) {
        const product = await this.productRepository.repository.findOne({
            select: {
                category: {
                    id: true,
                    code: true,
                    name: true,
                },
                productLevels: {
                    id: true,
                    price: true,
                    level: {
                        id: true,
                        name: true,
                    },
                },
                unit: {
                    id: true,
                    name: true,
                },
                branch: {
                    id: true,
                    name: true,
                },
            },
            where: { id },
            relations: {
                category: true,
                // productAttributes: {
                //   productAttributeValues: true,
                // },
                branch: true,
                unit: true,
                productLevels: {
                    level: true,
                },
                image: true,
            },
        });

        if (!product) throw new NotFoundException('product not found');

        if (product.productLevels) {
            product.productLevels.sort((a, b) => {
                if (a.level && b.level) {
                    return a.level.name.localeCompare(b.level.name);
                }
                return 0;
            });
        }

        return product;
    }

    async update(id: number, updateProductDto: UpdateProductDto) {
        const product = await this.findById(id);

        if (!product) throw new NotFoundException('product not found');

        await this.productRepository.updateProductLevel(id, updateProductDto);

        return this.findById(product.id);
    }

    async remove(id: number) {
        const product = await this.findById(id);

        if (!product) throw new NotFoundException('product not found');

        const now = +new Date();

        await this.productRepository.repository.update(id, {
            code: `${now}${product.code}`,
        });

        await this.productRepository.repository.softRemove(product);
    }

    findById(id: number) {
        return this.productRepository.repository.findOne({
            where: {
                id,
            },
            relations: {
                category: true,
                image: true,
            },
        });
    }

    async datatables(query: PaginateQuery): Promise<Paginated<Product>> {
        const products = await paginate(
            query,
            this.productRepository.repository,
            PRODUCT_PAGINATION_CONFIG,
        );

        if (query?.filter) {
            Object.keys(query.filter).forEach((k, v) => {
                if (k == 'productLevels.level.id') {
                    for (const data of products.data) {
                        if (data.productLevels.length) {
                            data.price = data.productLevels[0].price;
                        }
                    }
                }
            });
        }

        return products;
    }

    // findProductPriceByName(name: string, productId: number) {
    //   return this.productPriceRepository.findOne({
    //     relations: {
    //       product: true,
    //     },
    //     where: {
    //       name: name,
    //       product: {
    //         id: productId,
    //       }
    //     }
    //   });
    // }

    // createAttribute(productId: number, dto: CreateProductAttributeDto) {
    //   return this.productRepository.createAttribute(productId, dto);
    // }

    async deleteAttribute(productAttributeId: number) {
        const isExist =
            await this.productRepository.findAttribute(productAttributeId);
        if (!isExist) {
            throw new NotFoundException('product attribute not found');
        }

        await this.productRepository.deleteAttribute(productAttributeId);
    }

    async createProductLevel(createProductDto: CreateProductDto) {
        const product = await this.productRepository.createProductLevel({
            ...createProductDto,
        });

        return this.findOne(product.id);
    }

    async purchaseHistory(productId: number) {
        const queueItems = await QueueItem.find({
            select: {
                createdAt: true,
                netWeight: true,
                price: true,
                total: true,
                productName: true,
            },
            where: {
                product: { id: productId },
            },
            order: {
                createdAt: 'DESC',
            }
        });

        if (!queueItems || queueItems.length === 0) {
            throw new NotFoundException('No purchase history found for this product.');
        }

        const history = queueItems.map(item => ({
            purchaseDate: DateTime.fromJSDate(item.createdAt).toFormat('yyyy-MM-dd'),
            quantity: item.netWeight,
            pricePerUnit: item.price,
            totalAmount: item.total,
        }));

        const totalPurchaseAmount = history.reduce((sum, item) => sum + Number(item.totalAmount || 0), 0);

        return {
            productName: queueItems[0].productName,
            history,
            totalPurchaseAmount,
        };
    }
}
