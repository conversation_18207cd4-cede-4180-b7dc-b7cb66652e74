import { Module } from '@nestjs/common';
import { ProductService } from './product.service';
import { ProductController } from './product.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Product } from './entities/product.entity';
import { ProductRepository } from './product.repository';
import { ProductAttribute } from './entities/product-attribute.entity';
import { ProductAttributeValue } from './entities/product-attribute-value.entity';
import { ProductReportService } from './product-report.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Product, ProductAttribute, ProductAttributeValue])
  ],
  controllers: [ProductController],
  providers: [ProductService, ProductRepository, ProductReportService],
  exports: [ProductService]
})
export class ProductModule {}
