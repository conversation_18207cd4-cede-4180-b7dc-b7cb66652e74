import { Category } from "../../category/entities/category.entity";
import { CustomBaseEntity } from "../../common/entities";
import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from "typeorm";
import { OrderItem } from "../../order/entities/order-item.entity";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { Product } from "./product.entity";
import { ProductAttributeValue } from "./product-attribute-value.entity";

export enum ProductAttributeType {
  SINGLE = 'single',
  QUANTITY = 'quantity',
  MULTIPLE = 'multiple'
}

@Entity()
export class ProductAttribute extends CustomBaseEntity {
  @Column()
  name: string;

  @Column({ type: 'enum', enum: ProductAttributeType, nullable: true })
  type: ProductAttributeType;

  // @ManyToOne(() => Product, (_) => _.productAttributes)
  // @JoinColumn({ name: 'product_id' })
  // product: Product;

  @OneToMany(() => ProductAttributeValue, (_) => _.productAttribute)
  productAttributeValues: ProductAttributeValue[];

  constructor(partial?: Partial<ProductAttribute>) {
    super();
    if (partial) {
      Object.assign(this, partial)
    }
  }
}
