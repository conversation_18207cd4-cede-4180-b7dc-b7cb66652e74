import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { CustomBaseEntity } from "../../common/entities";
import { Level } from "../../level/entities/level.entity";
import { Product } from "../../product/entities/product.entity";
import { Column, Entity, JoinColumn, ManyToOne } from "typeorm";

@Entity()
export class ProductLevel extends CustomBaseEntity {
    @ManyToOne(() => Product, (p: Product) => p.productLevels)
    @JoinColumn({ name: 'product_id' })
    product: Product;

    @ManyToOne(() => Level, (l: Level) => l.productLevels)
    @JoinColumn({ name: 'level_id' })
    level: Level;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer()})
    price: number;
}
