import { Category } from "../../category/entities/category.entity";
import { CustomBaseEntity } from "../../common/entities";
import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from "typeorm";
import { OrderItem } from "../../order/entities/order-item.entity";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { Product } from "./product.entity";
import { ProductAttribute } from "./product-attribute.entity";

@Entity()
export class ProductAttributeValue extends CustomBaseEntity {
    @Column()
    name: string;

    @Column({
        type: 'numeric', nullable: true,
        transformer: new DecimalColumnTransformer(),
    })
    price: number;

    @ManyToOne(() => ProductAttribute, (_) => _.productAttributeValues)
    @JoinColumn({ name: 'product_attribute_id' })
    productAttribute: ProductAttribute;

    constructor(partial?: Partial<ProductAttributeValue>) {
        super();
        if (partial) {
            Object.assign(this, partial)
        }
    }
}
