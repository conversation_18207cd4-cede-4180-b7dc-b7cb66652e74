import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateProductDto } from './create-product.dto';
import { IsEnum, IsNotEmpty, ValidateIf } from 'class-validator';
import { Type } from './create-product.dto';
import { CreateProductLevelDto } from './create-product-level.dto';

export class UpdateProductDto {
    @ApiProperty()
    readonly code?: string;

    @ApiProperty()
    readonly name?: string;

    @ApiProperty()
    readonly price?: number;

    @ApiProperty({ enum: Type })
    @IsEnum(Type)
    readonly type?: Type;

    @ApiProperty()
    @ValidateIf(o => o.type && o.type === Type.IMAGE)
    @IsNotEmpty({ message: 'ต้องระบุ imageId ถ้า type เป็น image' })
    readonly imageId?: number;

    @ApiProperty()
    @ValidateIf(o => o.type && o.type === Type.COLOR)
    @IsNotEmpty({ message: 'ต้องระบุ colorCode ถ้า type เป็น color' })
    readonly colorCode?: string;

    readonly categoryId?: number;
    readonly unitId?: number;
    readonly branchId?: number;
    readonly productLevels?: CreateProductLevelDto[];
}
