import { IsNotEmpty } from "class-validator";
import { ProductAttributeType } from "../entities/product-attribute.entity";

export class CreateProductAttributeDto {
    readonly id?: number;

    @IsNotEmpty()
    readonly name: string;

    @IsNotEmpty()
    readonly type: ProductAttributeType;

    readonly attributeValues: CreateProductAttributeValueDto[];
}

export class CreateProductAttributeValueDto {
  readonly id?: number;

  @IsNotEmpty()
  readonly name: string;

  @IsNotEmpty()
  readonly price: number;
}
