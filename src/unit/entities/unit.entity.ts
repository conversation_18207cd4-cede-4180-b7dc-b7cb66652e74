import { Product } from "../../product/entities/product.entity";
import { CustomBaseEntity } from "../../common/entities";
import { Column, Entity, OneToMany } from "typeorm";
import { QueueItem } from "src/queue/entities/queue-item.entity";

@Entity()
export class Unit extends CustomBaseEntity {
    @Column()
    name: string;

    @OneToMany(() => Product, (_) => _.unit)
    products: Array<Product>;

    constructor(partial?: Partial<Unit>) {
        super();
        if (partial) {
            Object.assign(this, partial)
        }
    }

    @OneToMany(() => QueueItem, (queueItem) => queueItem.unit)
    queueItems: Array<QueueItem>;

}
