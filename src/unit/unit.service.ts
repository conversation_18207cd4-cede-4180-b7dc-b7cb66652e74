import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateUnitDto } from './dto/create-unit.dto';
import { UpdateUnitDto } from './dto/update-unit.dto';
import { Unit } from './entities/unit.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';

export const UNIT_PAGINATION_CONFIG: PaginateConfig<Unit> = {
  sortableColumns: ['id', 'name'],
  select: ['id', 'name', 'createdAt'],
};

@Injectable()
export class UnitService {
  constructor(
    @InjectRepository(Unit)
    private unitRepository: Repository<Unit>,
  ) { }

  create(createUnitDto: CreateUnitDto) {
    const unit = this.unitRepository.create(createUnitDto);

    return this.unitRepository.save(unit);
  }

  findAll() {
    return this.unitRepository.find();
  }

  async findOne(id: number) {
    const unit = await this.unitRepository.findOneBy({ id });

    if (!unit) {
      throw new NotFoundException(`unit #${id} not found`);
    }

    return unit;
  }

  async update(id: number, updateUnitDto: UpdateUnitDto) {
    const unit = await this.unitRepository.findOneBy({ id });

    if (!unit) {
      throw new NotFoundException(`unit #${id} not found`);
    }

    unit.name = updateUnitDto.name;

    return this.unitRepository.save(unit);
  }

  async remove(id: number) {
    const unit = await this.unitRepository.findOneBy({ id });

    if (!unit) {
      throw new NotFoundException(`unit #${id} not found`);
    }

    await this.unitRepository.remove(unit);
  }

  async datatables(query: PaginateQuery): Promise<Paginated<Unit>> {
    return paginate(query, this.unitRepository, UNIT_PAGINATION_CONFIG);
  }
}
