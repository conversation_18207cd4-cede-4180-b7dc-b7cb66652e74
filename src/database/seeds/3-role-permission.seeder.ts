import { DataSource } from "typeorm";
import { Seeder, SeederFactoryManager } from "typeorm-extension";

export default class RolePermissionSeeder implements Seeder {
    public async run(
        dataSource: DataSource,
        factoryManager: SeederFactoryManager,
    ): Promise<void> {
        await dataSource
            .createQueryBuilder()
            .insert()
            .into('role_permission')
            .values([
                { role_id: 1, permission_id: 1 },
                { role_id: 1, permission_id: 2 },
                { role_id: 1, permission_id: 3 },
                { role_id: 1, permission_id: 4 },
            ])
            .execute();
    }
}