import { DataSource } from "typeorm";
import { Seeder, SeederFactoryManager } from "typeorm-extension";
import { Branch } from "../../branch/entities/branch.entity";

export default class BranchSeeder implements Seeder {
  public async run(
    dataSource: DataSource,
    factoryManager: SeederFactoryManager,
  ): Promise<void> {
    await dataSource.query('TRUNCATE TABLE "branch" RESTART IDENTITY CASCADE;');

    const repository = dataSource.getRepository(Branch);
    await repository.insert([
      { code: '00000', name: 'Headquarter', address: 'BKK', store: { id: 1 } },
    ]);
  }
}