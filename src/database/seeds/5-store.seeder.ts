import { Store } from "../../store/entities/store.entity";
import { DataSource } from "typeorm";
import { Seeder, SeederFactoryManager } from "typeorm-extension";

export default class StoreSeeder implements Seeder {
  public async run(
    dataSource: DataSource,
    factoryManager: SeederFactoryManager,
  ): Promise<void> {
    await dataSource.query('TRUNCATE TABLE "store" RESTART IDENTITY CASCADE;');

    const repository = dataSource.getRepository(Store);
    await repository.insert([
      { code: '00000', name: 'Easter Egg', address: 'BKK' },
    ]);
  }
}