import { PaymentMethod, PaymentMethodType } from "../../payment-method/entities/payment-method.entity";
import { DataSource } from "typeorm";
import { Seeder, SeederFactoryManager } from "typeorm-extension";

export default class PaymentMethodSeeder implements Seeder {
    public async run(
        dataSource: DataSource,
        factoryManager: SeederFactoryManager,
    ): Promise<void> {
        await dataSource.query('TRUNCATE TABLE "payment_method" RESTART IDENTITY CASCADE;');

        const repository = dataSource.getRepository(PaymentMethod);
        await repository.insert([
            { name: 'เงินสด', type: PaymentMethodType.CASH, icon: 'static/icon/cash.svg'},
            { name: 'พร้อมเพย์', type: PaymentMethodType.THAIQR, icon: 'static/icon/pp.svg'},
        ]);
    }
}