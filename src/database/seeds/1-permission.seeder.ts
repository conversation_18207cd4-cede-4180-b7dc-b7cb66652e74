import { Permission } from "../../permission/entities/permission.entity";
import { DataSource } from "typeorm";
import { Seeder, SeederFactoryManager } from "typeorm-extension";

export default class PermissionSeeder implements Seeder {
    public async run(
        dataSource: DataSource,
        factoryManager: SeederFactoryManager,
    ): Promise<void> {
        await dataSource.query('TRUNCATE TABLE "permission" RESTART IDENTITY CASCADE;');

        const repository = dataSource.getRepository(Permission);
        await repository.insert([
            { name: 'ADMIN_CREATE' },
            { name: 'ADMIN_READ' },
            { name: 'ADMIN_UPDATE' },
            { name: 'ADMIN_DELETE' },
        ]);
    }
}