import { Role } from "../../role/entities/role.entity";
import { DataSource } from "typeorm";
import { Seeder, SeederFactoryManager } from "typeorm-extension";

export default class RoleSeeder implements Seeder {
    public async run(
        dataSource: DataSource,
        factoryManager: SeederFactoryManager,
    ): Promise<void> {
        await dataSource.query('TRUNCATE TABLE "role" RESTART IDENTITY CASCADE;');

        const repository = dataSource.getRepository(Role);
        await repository.insert([
            { name: 'super_admin' },
            { name: 'admin' },
            { name: 'supervisor' },
            { name: 'cashier' },
        ]);
    }
}