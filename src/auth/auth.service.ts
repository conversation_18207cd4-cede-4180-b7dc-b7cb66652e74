import * as argon2 from 'argon2';

import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UserService } from 'src/user/user.service';
import { ConfigService } from '@nestjs/config';
import { AuthDto } from './dto/auth.dto';
import { CreateUserDto } from 'src/user/dto/create-user.dto';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UserService,
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  async createSuperAdmin(createUserDto: CreateUserDto) {
    try {
      await this.usersService.create({
        code: createUserDto.code,
        username: createUserDto.username,
        password: createUserDto.password,
        firstName: createUserDto.firstName,
        lastName: createUserDto.lastName,
        phoneNumber: null,
        roleId: 1,
      });
    } catch (error) {
      throw new BadRequestException();
    }
  }

  async signIn(authDto: AuthDto) {
    const user = await this.usersService.findByUsername(authDto.username);
    if (!user)
      throw new UnauthorizedException('username or password is not correct');

    const passwordMatches = await argon2.verify(
      user.password,
      authDto.password,
    );
    if (!passwordMatches)
      throw new UnauthorizedException('username or password is not correct');

    const tokens = await this.getTokens(user.id, user.username,authDto.branch_id);
    await this.updateRefreshToken(user.id, tokens.refreshToken);
    return tokens;
  }

  async getTokens(userId: number, username: string, branch_id: number) {
    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(
        { sub: userId, username, branch_id },
        {
          secret: this.configService.get('JWT_ACCESS_SECRET'),
          expiresIn: '1d',
        },
      ),
      this.jwtService.signAsync(
        { sub: userId, username, branch_id },
        {
          secret: this.configService.get('JWT_REFRESH_SECRET'),
          expiresIn: '7d',
        },
      ),
    ]);

    return {
      accessToken,
      refreshToken,
      name: username
    };

    
  }

  async updateRefreshToken(userId: number, refreshToken: string) {
    const hash = await argon2.hash(refreshToken);
    await this.usersService.updateRefreshToken(userId, hash);
  }

  async logout(userId: number) {
    await this.usersService.updateRefreshToken(userId, null);
  }

  async refreshTokens(userId: number, refreshToken: string) {
    const user = await this.usersService.findOne(userId);
    if (!user || !user.refreshToken)
      throw new ForbiddenException('Access Denied');
    const refreshTokenMatches = await argon2.verify(
      user.refreshToken,
      refreshToken,
    );
    if (!refreshTokenMatches) throw new ForbiddenException('Access Denied');
    const tokens = await this.getTokens(user.id, user.username,null);
    await this.updateRefreshToken(user.id, tokens.refreshToken);
    return tokens;
  }

  async signInWithToken(accessToken: string) {
    const data = this.jwtService.decode(accessToken);

    const user = await this.usersService.findByUsername(data.username);
    if (!user) throw new UnauthorizedException('user not found');

    const tokens = await this.getTokens(user.id, user.username,null);
    await this.updateRefreshToken(user.id, tokens.refreshToken);
    return tokens;
  }
}
