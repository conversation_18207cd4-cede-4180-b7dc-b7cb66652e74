import { Injectable, NotFoundException } from '@nestjs/common';
import { CreatePaymentMethodDto } from './dto/create-payment-method.dto';
import { UpdatePaymentMethodDto } from './dto/update-payment-method.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PaymentMethod } from './entities/payment-method.entity';

@Injectable()
export class PaymentMethodService {
  constructor(
    @InjectRepository(PaymentMethod)
    private paymentMethodRepository: Repository<PaymentMethod>,
  ) { }

  // create(createPaymentMethodDto: CreatePaymentMethodDto) {

  // }

  async findAll() {
    const data = await this.paymentMethodRepository.find();
  
    data.forEach(e => {
      e.icon = process.env.APP_URL + '/' + e.icon;
    });

    return data;
  }

  async findOne(id: number) {
    const paymentMethod = await this.paymentMethodRepository.findOneBy({ id });

    if (!paymentMethod) {
      throw new NotFoundException(`No payment method`);
    }

    return paymentMethod;
  }

  // update(id: number, updatePaymentMethodDto: UpdatePaymentMethodDto) {
  //   return `This action updates a #${id} paymentMethod`;
  // }

  // async remove(id: number) {
  //   await this.paymentMethodRepository.delete(id);
  // }
}
