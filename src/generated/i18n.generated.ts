/* DO NOT EDIT, file generated by nestjs-i18n */

/* eslint-disable */
/* prettier-ignore */
import { Path } from "nestjs-i18n";
/* prettier-ignore */
export type I18nTranslations = {
    "branch": {
        "NOT_FOUND": string;
    };
    "car-type": {
        "NOT_FOUND": string;
    };
    "product": {
        "NOT_FOUND": string;
    };
    "queue": {
        "NOT_FOUND": string;
        "SAVE_FAILED": string;
        "PHOTO_SAVE_FAILED": string;
        "PHOTO_UPDATE_FAILED": string;
    };
    "unit": {
        "NOT_FOUND": string;
    };
};
/* prettier-ignore */
export type I18nPath = Path<I18nTranslations>;
