import { CustomBaseEntity } from '../../common/entities/custom-base.entity';
import { Role } from '../../role/entities/role.entity';
import {
  Column,
  Entity,
  Index,
  ManyToMany,
  ManyToOne,
  Unique,
} from 'typeorm';
import { PermissionGroup } from './permission-group.entity';

export enum PermissionType {
  // Admin
  ADMIN_USER_READ = 'ADMIN_USER_READ',
  ADMIN_USER_CREATE = 'ADMIN_USER_CREATE',
  ADMIN_USER_UPDATE = 'ADMIN_USER_UPDATE',
  ADMIN_USER_DELETE = 'ADMIN_USER_DELETE',

  ADMIN_SITE_READ = 'ADMIN_SITE_READ',
  ADMIN_SITE_CREATE = 'ADMIN_SITE_CREATE',
  ADMIN_SITE_UPDATE = 'ADMIN_SITE_UPDATE',
  ADMIN_SITE_DELETE = 'ADMIN_SITE_DELETE',

  // Human Resources - Employee
  HR_EMPLOYEE_READ = 'HR_EMPLOYEE_READ',
  HR_EMPLOYEE_CREATE = 'HR_EMPLOYEE_CREATE',
  HR_EMPLOYEE_UPDATE = 'HR_EMPLOYEE_UPDATE',
  HR_EMPLOYEE_DELETE = 'HR_EMPLOYEE_DELETE',

  HR_EMPLOYEE_SETTING_READ = 'HR_EMPLOYEE_SETTING_READ',
  HR_EMPLOYEE_SETTING_CREATE = 'HR_EMPLOYEE_SETTING_CREATE',
  HR_EMPLOYEE_SETTING_UPDATE = 'HR_EMPLOYEE_SETTING_UPDATE',
  HR_EMPLOYEE_SETTING_DELETE = 'HR_EMPLOYEE_SETTING_DELETE',

  HR_EMPLOYEE_TYPE_READ = 'HR_EMPLOYEE_TYPE_READ',
  HR_EMPLOYEE_TYPE_CREATE = 'HR_EMPLOYEE_TYPE_CREATE',
  HR_EMPLOYEE_TYPE_UPDATE = 'HR_EMPLOYEE_TYPE_UPDATE',
  HR_EMPLOYEE_TYPE_DELETE = 'HR_EMPLOYEE_TYPE_DELETE',

  HR_EMPLOYEE_TITLE_READ = 'HR_EMPLOYEE_TITLE_READ',
  HR_EMPLOYEE_TITLE_CREATE = 'HR_EMPLOYEE_TITLE_CREATE',
  HR_EMPLOYEE_TITLE_UPDATE = 'HR_EMPLOYEE_TITLE_UPDATE',
  HR_EMPLOYEE_TITLE_DELETE = 'HR_EMPLOYEE_TITLE_DELETE',

  HR_EMPLOYEE_GROUP_READ = 'HR_EMPLOYEE_GROUP_READ',
  HR_EMPLOYEE_GROUP_CREATE = 'HR_EMPLOYEE_GROUP_CREATE',
  HR_EMPLOYEE_GROUP_UPDATE = 'HR_EMPLOYEE_GROUP_UPDATE',
  HR_EMPLOYEE_GROUP_DELETE = 'HR_EMPLOYEE_GROUP_DELETE',

  HR_EMPLOYEE_DEPARTMENT_READ = 'HR_EMPLOYEE_DEPARTMENT_READ',
  HR_EMPLOYEE_DEPARTMENT_CREATE = 'HR_EMPLOYEE_DEPARTMENT_CREATE',
  HR_EMPLOYEE_DEPARTMENT_UPDATE = 'HR_EMPLOYEE_DEPARTMENT_UPDATE',
  HR_EMPLOYEE_DEPARTMENT_DELETE = 'HR_EMPLOYEE_DEPARTMENT_DELETE',

  HR_EMPLOYEE_LEVEL_READ = 'HR_EMPLOYEE_LEVEL_READ',
  HR_EMPLOYEE_LEVEL_CREATE = 'HR_EMPLOYEE_LEVEL_CREATE',
  HR_EMPLOYEE_LEVEL_UPDATE = 'HR_EMPLOYEE_LEVEL_UPDATE',
  HR_EMPLOYEE_LEVEL_DELETE = 'HR_EMPLOYEE_LEVEL_DELETE',

  HR_EMPLOYEE_LEVEL_TYPE_READ = 'HR_EMPLOYEE_LEVEL_TYPE_READ',
  HR_EMPLOYEE_LEVEL_TYPE_CREATE = 'HR_EMPLOYEE_LEVEL_TYPE_CREATE',
  HR_EMPLOYEE_LEVEL_TYPE_UPDATE = 'HR_EMPLOYEE_LEVEL_TYPE_UPDATE',
  HR_EMPLOYEE_LEVEL_TYPE_DELETE = 'HR_EMPLOYEE_LEVEL_TYPE_DELETE',

  HR_EMPLOYEE_APPROVE_LIST_READ = 'HR_EMPLOYEE_APPROVE_LIST_READ',
  HR_EMPLOYEE_APPROVE_LIST_CREATE = 'HR_EMPLOYEE_APPROVE_LIST_CREATE',
  HR_EMPLOYEE_APPROVE_LIST_UPDATE = 'HR_EMPLOYEE_APPROVE_LIST_UPDATE',
  HR_EMPLOYEE_APPROVE_LIST_DELETE = 'HR_EMPLOYEE_APPROVE_LIST_DELETE',

  HR_EMPLOYEE_COMPANY_READ = 'HR_EMPLOYEE_COMPANY_READ',
  HR_EMPLOYEE_COMPANY_CREATE = 'HR_EMPLOYEE_COMPANY_CREATE',
  HR_EMPLOYEE_COMPANY_UPDATE = 'HR_EMPLOYEE_COMPANY_UPDATE',
  HR_EMPLOYEE_COMPANY_DELETE = 'HR_EMPLOYEE_COMPANY_DELETE',

  // Leave
  HR_LEAVE_READ = 'HR_LEAVE_READ',
  HR_LEAVE_CREATE = 'HR_LEAVE_CREATE',
  HR_LEAVE_UPDATE = 'HR_LEAVE_UPDATE',
  HR_LEAVE_DELETE = 'HR_LEAVE_DELETE',

  HR_LEAVE_SETTING_READ = 'HR_LEAVE_SETTING_READ',
  HR_LEAVE_SETTING_CREATE = 'HR_LEAVE_SETTING_CREATE',
  HR_LEAVE_SETTING_UPDATE = 'HR_LEAVE_SETTING_UPDATE',
  HR_LEAVE_SETTING_DELETE = 'HR_LEAVE_SETTING_DELETE',

  HR_LEAVE_TYPE_READ = 'HR_LEAVE_TYPE_READ',
  HR_LEAVE_TYPE_CREATE = 'HR_LEAVE_TYPE_CREATE',
  HR_LEAVE_TYPE_UPDATE = 'HR_LEAVE_TYPE_UPDATE',
  HR_LEAVE_TYPE_DELETE = 'HR_LEAVE_TYPE_DELETE',

  HR_WORK_SHIFT_READ = 'HR_WORK_SHIFT_READ',
  HR_WORK_SHIFT_CREATE = 'HR_WORK_SHIFT_CREATE',
  HR_WORK_SHIFT_UPDATE = 'HR_WORK_SHIFT_UPDATE',
  HR_WORK_SHIFT_DELETE = 'HR_WORK_SHIFT_DELETE',

  HR_COMPANY_HOLIDAY_READ = 'HR_COMPANY_HOLIDAY_READ',
  HR_COMPANY_HOLIDAY_CREATE = 'HR_COMPANY_HOLIDAY_CREATE',
  HR_COMPANY_HOLIDAY_UPDATE = 'HR_COMPANY_HOLIDAY_UPDATE',
  HR_COMPANY_HOLIDAY_DELETE = 'HR_COMPANY_HOLIDAY_DELETE',

  // Overtime
  HR_OT_READ = 'HR_OT_READ',
  HR_OT_CREATE = 'HR_OT_CREATE',
  HR_OT_UPDATE = 'HR_OT_UPDATE',
  HR_OT_DELETE = 'HR_OT_DELETE',

  HR_OT_AIR_READ = 'HR_OT_AIR_READ',
  HR_OT_AIR_CREATE = 'HR_OT_AIR_CREATE',
  HR_OT_AIR_UPDATE = 'HR_OT_AIR_UPDATE',
  HR_OT_AIR_DELETE = 'HR_OT_AIR_DELETE',

  HR_OT_SETTING_READ = 'HR_OT_SETTING_READ',
  HR_OT_SETTING_CREATE = 'HR_OT_SETTING_CREATE',
  HR_OT_SETTING_UPDATE = 'HR_OT_SETTING_UPDATE',
  HR_OT_SETTING_DELETE = 'HR_OT_SETTING_DELETE',

  HR_OT_ZONE_READ = 'HR_OT_ZONE_READ',
  HR_OT_ZONE_CREATE = 'HR_OT_ZONE_CREATE',
  HR_OT_ZONE_UPDATE = 'HR_OT_ZONE_UPDATE',
  HR_OT_ZONE_DELETE = 'HR_OT_ZONE_DELETE',

  HR_OT_FLOOR_READ = 'HR_OT_FLOOR_READ',
  HR_OT_FLOOR_CREATE = 'HR_OT_FLOOR_CREATE',
  HR_OT_FLOOR_UPDATE = 'HR_OT_FLOOR_UPDATE',
  HR_OT_FLOOR_DELETE = 'HR_OT_FLOOR_DELETE',

  HR_OT_SETTING_EMAIL_READ = 'HR_OT_SETTING_EMAIL_READ',
  HR_OT_SETTING_EMAIL_CREATE = 'HR_OT_SETTING_EMAIL_CREATE',
  HR_OT_SETTING_EMAIL_UPDATE = 'HR_OT_SETTING_EMAIL_UPDATE',
  HR_OT_SETTING_EMAIL_DELETE = 'HR_OT_SETTING_EMAIL_DELETE',

  // Project
  HR_PROJECT_READ = 'HR_PROJECT_READ',
  HR_PROJECT_CREATE = 'HR_PROJECT_CREATE',
  HR_PROJECT_UPDATE = 'HR_PROJECT_UPDATE',
  HR_PROJECT_DELETE = 'HR_PROJECT_DELETE',

  // Report - Leave
  REPORT_LEAVE_ACCUMULATE_STAFF = 'REPORT_LEAVE_ACCUMULATE_STAFF',
  REPORT_LEAVE_DETAIL_ACCUMULATE_STAFF = 'REPORT_LEAVE_DETAIL_ACCUMULATE_STAFF',

  // Report - Time Attendance
  REPORT_TA_VIEW_DAILY_RAW = 'REPORT_TA_VIEW_DAILY_RAW',
  REPORT_TA_VIEW_DAILY_CHECKIN = 'REPORT_TA_VIEW_DAILY_CHECKIN',
  REPORT_TA_VIEW_BY_USER = 'REPORT_TA_VIEW_BY_USER',
  REPORT_TA_DAILY_ATTENDANCE = 'REPORT_TA_DAILY_ATTENDANCE',
  REPORT_TA_MONTHLY_ATTENDANCE = 'REPORT_TA_MONTHLY_ATTENDANCE',
  REPORT_TA_SUMMARY_DEDUCTION = 'REPORT_TA_SUMMARY_DEDUCTION',

  // Report - Overtime
  REPORT_OT_REQUEST_GROUP = 'REPORT_OT_REQUEST_GROUP',
  REPORT_OT_TA_COMPARISON = 'REPORT_OT_TA_COMPARISON',
  REPORT_OT_VIEW_BY_OWNER = 'REPORT_OT_VIEW_BY_OWNER',
  REPORT_OT_PENDING_REQUEST = 'REPORT_OT_PENDING_REQUEST',

  // Report - Overtime Air
  REPORT_OT_AIR_REQUEST = 'REPORT_OT_AIR_REQUEST',

  // Client List
  CLIENT_LIST_READ = 'CLIENT_LIST_READ',
  CLIENT_LIST_CREATE = 'CLIENT_LIST_CREATE',
  CLIENT_LIST_UPDATE = 'CLIENT_LIST_UPDATE',
  CLIENT_LIST_DELETE = 'CLIENT_LIST_DELETE',

  CLIENT_COMPANY_READ = 'CLIENT_COMPANY_READ',
  CLIENT_COMPANY_CREATE = 'CLIENT_COMPANY_CREATE',
  CLIENT_COMPANY_UPDATE = 'CLIENT_COMPANY_UPDATE',
  CLIENT_COMPANY_DELETE = 'CLIENT_COMPANY_DELETE',

  CLIENT_CUSTOMER_READ = 'CLIENT_CUSTOMER_READ',
  CLIENT_CUSTOMER_CREATE = 'CLIENT_CUSTOMER_CREATE',
  CLIENT_CUSTOMER_UPDATE = 'CLIENT_CUSTOMER_UPDATE',
  CLIENT_CUSTOMER_DELETE = 'CLIENT_CUSTOMER_DELETE',

  CLIENT_SETTING_CATEGORY_READ = 'CLIENT_SETTING_CATEGORY_READ',
  CLIENT_SETTING_CATEGORY_CREATE = 'CLIENT_SETTING_CATEGORY_CREATE',
  CLIENT_SETTING_CATEGORY_UPDATE = 'CLIENT_SETTING_CATEGORY_UPDATE',
  CLIENT_SETTING_CATEGORY_DELETE = 'CLIENT_SETTING_CATEGORY_DELETE',

  CLIENT_SETTING_ACTIVITY_READ = 'CLIENT_SETTING_ACTIVITY_READ',
  CLIENT_SETTING_ACTIVITY_CREATE = 'CLIENT_SETTING_ACTIVITY_CREATE',
  CLIENT_SETTING_ACTIVITY_UPDATE = 'CLIENT_SETTING_ACTIVITY_UPDATE',
  CLIENT_SETTING_ACTIVITY_DELETE = 'CLIENT_SETTING_ACTIVITY_DELETE',

  // Time Attendance - Adjust Review
  TA_ADJUST_REVIEW_READ = 'TA_ADJUST_REVIEW_READ',
  TA_ADJUST_REVIEW_CREATE = 'TA_ADJUST_REVIEW_CREATE',
  TA_ADJUST_REVIEW_UPDATE = 'TA_ADJUST_REVIEW_UPDATE',
  TA_ADJUST_REVIEW_DELETE = 'TA_ADJUST_REVIEW_DELETE',

  // Recruitment
  RECRUITMENT_JOB_READ = 'RECRUITMENT_JOB_READ',
  RECRUITMENT_JOB_CREATE = 'RECRUITMENT_JOB_CREATE',
  RECRUITMENT_JOB_UPDATE = 'RECRUITMENT_JOB_UPDATE',
  RECRUITMENT_JOB_DELETE = 'RECRUITMENT_JOB_DELETE',

  RECRUITMENT_APPLICANT_READ = 'RECRUITMENT_APPLICANT_READ',
  RECRUITMENT_APPLICANT_CREATE = 'RECRUITMENT_APPLICANT_CREATE',
  RECRUITMENT_APPLICANT_UPDATE = 'RECRUITMENT_APPLICANT_UPDATE',
  RECRUITMENT_APPLICANT_DELETE = 'RECRUITMENT_APPLICANT_DELETE',

  RECRUITMENT_PERSONNEL_REQUEST_READ = 'RECRUITMENT_PERSONNEL_REQUEST_READ',
  RECRUITMENT_PERSONNEL_REQUEST_CREATE = 'RECRUITMENT_PERSONNEL_REQUEST_CREATE',
  RECRUITMENT_PERSONNEL_REQUEST_UPDATE = 'RECRUITMENT_PERSONNEL_REQUEST_UPDATE',
  RECRUITMENT_PERSONNEL_REQUEST_DELETE = 'RECRUITMENT_PERSONNEL_REQUEST_DELETE',

  RECRUITMENT_JOB_APPLICATION_READ = 'RECRUITMENT_JOB_APPLICATION_READ',
  RECRUITMENT_JOB_APPLICATION_CREATE = 'RECRUITMENT_JOB_APPLICATION_CREATE',
  RECRUITMENT_JOB_APPLICATION_UPDATE = 'RECRUITMENT_JOB_APPLICATION_UPDATE',
  RECRUITMENT_JOB_APPLICATION_DELETE = 'RECRUITMENT_JOB_APPLICATION_DELETE',
}


@Entity()
@Unique(['name'])
export class Permission extends CustomBaseEntity {
  @Column({ comment: 'Permission name' })
  @Index({ unique: true })
  name: string;

  @Column({ nullable: true, comment: 'Permission description' })
  description: string;

  @ManyToMany(() => Role, (_) => _.permissions)
  roles: Array<Role>;

  @ManyToOne(() => PermissionGroup, (_) => _.permissions)
  permissionGroup: PermissionGroup;
}
