import { IsNotEmpty, <PERSON>N<PERSON><PERSON> } from "class-validator";
import { PromotionDisplay, PromotionType } from "../entities/promotion.entity";

export class CreatePromotionDto {
  @IsNotEmpty()
  readonly code: string;

  @IsNotEmpty()
  readonly name: string;

  @IsNotEmpty()
  readonly detail: string;

  @IsNotEmpty()
  @IsNumber()
  readonly amount: number;

  @IsNotEmpty()
  readonly display: PromotionDisplay;

  @IsNotEmpty()
  readonly startDate: Date;

  @IsNotEmpty()
  readonly endDate: Date;

  @IsNotEmpty()
  readonly type: PromotionType;

  @IsNotEmpty()
  readonly isActive: boolean;
}
