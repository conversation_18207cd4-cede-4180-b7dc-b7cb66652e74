import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreatePromotionDto } from './dto/create-promotion.dto';
import { UpdatePromotionDto } from './dto/update-promotion.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Promotion, PromotionDisplay } from './entities/promotion.entity';
import { Repository } from 'typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';

export const PROMOTION_PAGINATION_CONFIG : PaginateConfig<Promotion> = {
  sortableColumns: ['id', 'code', 'name'],
  searchableColumns: ['code', 'name'],
  filterableColumns: {
    type: true,
  },
  relativePath: true,
};
@Injectable()
export class PromotionService {
  constructor(
    @InjectRepository(Promotion)
    private promotionRepository: Repository<Promotion>,
  ) { }
  create(createPromotionDto: CreatePromotionDto) {
    if (createPromotionDto.display == PromotionDisplay.PERCENT) {
      if (createPromotionDto.amount < 0 || createPromotionDto.amount > 100) {
        throw new BadRequestException('percent is 0-100');
      }
    }

    const promotion = this.promotionRepository.create(createPromotionDto);

    return this.promotionRepository.save(promotion);
  }

  findAll() {
    return this.promotionRepository.find();
  }

  async findOne(id: number) {
    const promotion = await this.promotionRepository.findOneBy({ id })
    if (!promotion) throw new NotFoundException("promotion not found");

    return promotion;
  }

  async update(id: number, updatePromotionDto: UpdatePromotionDto) {
    const promotion = await this.promotionRepository.findOneBy({ id })
    if (!promotion) throw new NotFoundException("promotion not found");

    return this.promotionRepository.update(id, updatePromotionDto);
  }

  async remove(id: number) {
    const promotion = await this.promotionRepository.findOneBy({ id })
    if (!promotion) throw new NotFoundException("promotion not found");

    await this.promotionRepository.softRemove(promotion);
  }

  async datatables(query: PaginateQuery): Promise<Paginated<Promotion>> {
    return paginate(query, this.promotionRepository, PROMOTION_PAGINATION_CONFIG);
  }
}
