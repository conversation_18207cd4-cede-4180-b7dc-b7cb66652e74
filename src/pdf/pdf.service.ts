import { Injectable, NotFoundException } from '@nestjs/common';
import * as puppeteer from 'puppeteer';
import * as fs from 'fs';
import * as path from 'path';
import * as Handlebars from 'handlebars';
import { Order } from 'src/order/entities/order.entity';

@Injectable()
export class PdfService {

  async generatePdfBuffer(): Promise<Buffer> {
    const templateHtml = fs.readFileSync(
      path.join(__dirname, 'templates', 'report.hbs'),
      'utf8'
    );

    const template = Handlebars.compile(templateHtml);

    const data = {
      date: new Date().toLocaleDateString('th-TH'),
      users: [
        { name: 'สมชาย ใจดี', email: '<EMAIL>' },
        { name: 'สมหญิง น่ารัก', email: '<EMAIL>' },
      ],
      fontPath: path.resolve(__dirname, '../../assets/fonts'),
    };

    const html = template(data);

    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });

    const page = await browser.newPage();

    await page.setContent(html, { waitUntil: 'networkidle0' });

    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
    });

    await browser.close();

    return Buffer.from(pdfBuffer);
  }

  async generateOrderPdfBuffer(orderId: number): Promise<Buffer> {
    const order = await Order.findOne({
      where: { id: orderId },
      relations: {
        orderItems: {
          product: {
            category: true
          },
          attributes: {
            attributeValues: true,
          }
        },
        orderPayments: {
          paymentMethod: true
        },
        user: true,
        customer: {
          identityCard: true,
          // level: true
        },
        paymentMethod: true,
        customerBank: true,
        branch: true,
        photos: true,
      },
    });

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    const templateHtml = fs.readFileSync(
      path.join(__dirname, 'templates', 'order.hbs'),
      'utf8'
    );

    Handlebars.registerHelper('formatDate', (dateStr: string) => {
      const date = new Date(dateStr);
      return date.toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' });
    });

    Handlebars.registerHelper('formatCurrency', (num: number) => {
      return new Intl.NumberFormat('th-TH', {
        style: 'currency',
        currency: 'THB',
      }).format(num || 0);
    });

    Handlebars.registerHelper('inc', function (value: string) {
      return parseInt(value) + 1;
    });

    const template = Handlebars.compile(templateHtml);

    const data = {
      ...order,
    };

    const html = template(data);

    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });

    const page = await browser.newPage();

    await page.setContent(html, { waitUntil: 'networkidle0' });

    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
    });

    await browser.close();

    return Buffer.from(pdfBuffer);
  }
}
