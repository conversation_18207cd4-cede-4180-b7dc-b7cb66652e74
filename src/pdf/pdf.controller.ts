import { Controller, Get, Param, Post, Res, StreamableFile } from '@nestjs/common';
import { PdfService } from './pdf.service';
import { Response } from 'express';

@Controller('pdf')
export class PdfController {
  constructor(private readonly pdfService: PdfService) { }

  @Post('report')
  async getPdf(@Res({ passthrough: true }) res: Response) {
    const buffer = await this.pdfService.generatePdfBuffer();

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="report.pdf"`);
    res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');

    return new StreamableFile(buffer);
  }

  @Post('order/:id')
  async getOrderPdf(@Res({ passthrough: true }) res: Response, @Param('id') id: string) {
    const buffer = await this.pdfService.generateOrderPdfBuffer(+id);

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `inline; filename="order.pdf"`);
    res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');

    return new StreamableFile(buffer);
  }
}