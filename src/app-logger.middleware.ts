import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class AppLoggerMiddleware implements NestMiddleware {
  private logger = new Logger('HTTP');

  use(request: Request, response: Response, next: NextFunction): void {
    const { method, path: url, baseUrl } = request;
    const userAgent = request.get('user-agent') || '';
    const ip = request.headers['x-forwarded-for'] || request.socket.remoteAddress

    let log = {
      // hostname: request.hostname,
      // baseUrl: request.baseUrl,
      // originalUrl: request.originalUrl,
      // headers: {
      //   ...request.headers,
      // },
      // params: {
      //   ...request.params,
      // },
      query: {
        ...request.query,
      },
      body: {
        ...request.body,
      }
    }

    response.on('close', () => {
      const { statusCode } = response;

      if (statusCode == 500) {
        this.logger.error(`[${method}] ${baseUrl} - ${statusCode} : ${ip} | ` + JSON.stringify(log));
      } else {
        this.logger.verbose(`[${method}] ${baseUrl} - ${statusCode} : ${ip} | ` + JSON.stringify(log));
      }
    });

    next();
  }
}
