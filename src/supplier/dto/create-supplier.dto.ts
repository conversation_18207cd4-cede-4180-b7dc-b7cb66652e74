import { ApiProperty } from "@nestjs/swagger";
import { IsBoolean, IsEmail, IsNotEmpty, IsOptional, IsString, IsUrl } from "class-validator";

export class CreateSupplierDto {
    @ApiProperty({ description: 'รหัสผู้จำหน่าย' })
    @IsNotEmpty()
    @IsString()
    code: string;

    @ApiProperty({ description: 'ชื่อผู้จำหน่าย' })
    @IsNotEmpty()
    @IsString()
    name: string;

    @ApiProperty({ description: 'ชื่อผู้ติดต่อ', required: false })
    @IsOptional()
    @IsString()
    contactPerson?: string;

    @ApiProperty({ description: 'หมายเลขโทรศัพท์', required: false })
    @IsOptional()
    @IsString()
    phone?: string;

    @ApiProperty({ description: 'อีเมล', required: false })
    @IsOptional()
    @IsEmail()
    email?: string;

    @ApiProperty({ description: 'ที่อยู่', required: false })
    @IsOptional()
    @IsString()
    address?: string;

    @ApiProperty({ description: 'เลขประจำตัวผู้เสียภาษี', required: false })
    @IsOptional()
    @IsString()
    taxId?: string;

    @ApiProperty({ description: 'เว็บไซต์', required: false })
    @IsOptional()
    @IsUrl()
    website?: string;

    @ApiProperty({ description: 'หมายเหตุ', required: false })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiProperty({ description: 'สถานะการใช้งาน', default: true })
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;
}
