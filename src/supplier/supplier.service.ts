import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PaginateConfig, PaginateQuery, paginate } from 'nestjs-paginate';
import { Supplier } from './entities/supplier.entity';
import { CreateSupplierDto } from './dto/create-supplier.dto';
import { UpdateSupplierDto } from './dto/update-supplier.dto';

const SUPPLIER_PAGINATION_CONFIG: PaginateConfig<Supplier> = {
  sortableColumns: ['id', 'code', 'name', 'createdAt'],
  nullSort: 'last',
  defaultSortBy: [['createdAt', 'DESC']],
  searchableColumns: ['code', 'name', 'contactPerson', 'phone', 'email'],
  select: [
    'id',
    'code', 
    'name',
    'contactPerson',
    'phone',
    'email',
    'address',
    'taxId',
    'website',
    'notes',
    'isActive',
    'createdAt',
    'updatedAt'
  ],
  filterableColumns: {
    isActive: true,
  },
};

@Injectable()
export class SupplierService {
  constructor(
    @InjectRepository(Supplier)
    private readonly supplierRepository: Repository<Supplier>,
  ) {}

  async datatables(query: PaginateQuery) {
    return paginate(query, this.supplierRepository, SUPPLIER_PAGINATION_CONFIG);
  }

  async findAll() {
    return this.supplierRepository.find({
      where: { isActive: true },
      order: {
        createdAt: 'DESC'
      }
    });
  }

  async findOne(id: number) {
    const supplier = await this.supplierRepository.findOne({
      where: { id },
      relations: {
        purchaseOrders: true
      }
    });

    if (!supplier) {
      throw new NotFoundException(`ไม่พบผู้จำหน่ายรหัส ${id}`);
    }

    return supplier;
  }

  async findByCode(code: string) {
    return this.supplierRepository.findOne({
      where: { code }
    });
  }

  async create(createSupplierDto: CreateSupplierDto) {
    // ตรวจสอบรหัสผู้จำหน่ายซ้ำ
    const existingSupplier = await this.findByCode(createSupplierDto.code);
    if (existingSupplier) {
      throw new BadRequestException(`รหัสผู้จำหน่าย ${createSupplierDto.code} มีอยู่แล้ว`);
    }

    const supplier = this.supplierRepository.create(createSupplierDto);
    return await this.supplierRepository.save(supplier);
  }

  async update(id: number, updateSupplierDto: UpdateSupplierDto) {
    const existingSupplier = await this.findOne(id);

    // ตรวจสอบรหัสผู้จำหน่ายซ้ำ (ถ้ามีการเปลี่ยนรหัส)
    if (updateSupplierDto.code && updateSupplierDto.code !== existingSupplier.code) {
      const duplicateSupplier = await this.findByCode(updateSupplierDto.code);
      if (duplicateSupplier) {
        throw new BadRequestException(`รหัสผู้จำหน่าย ${updateSupplierDto.code} มีอยู่แล้ว`);
      }
    }

    await this.supplierRepository.update(id, updateSupplierDto);
    return this.findOne(id);
  }

  async remove(id: number) {
    const existingSupplier = await this.findOne(id);
    
    // ตรวจสอบว่ามี PurchaseOrder ที่เชื่อมโยงอยู่หรือไม่
    if (existingSupplier.purchaseOrders && existingSupplier.purchaseOrders.length > 0) {
      throw new BadRequestException('ไม่สามารถลบผู้จำหน่ายที่มีใบสั่งซื้อเชื่อมโยงอยู่');
    }

    return await this.supplierRepository.softDelete(id);
  }

  async toggleActive(id: number) {
    const supplier = await this.findOne(id);
    await this.supplierRepository.update(id, { isActive: !supplier.isActive });
    return this.findOne(id);
  }
}
