import { Controller, Get, Post, Body, Put, Param, Delete, Query, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBody, ApiQuery } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { DashboardService } from './dashboard.service';


@Controller('dashboard')
@ApiTags('dashboard')
@Auth()
export class DashboardController {

    constructor(private readonly dashboardService: DashboardService) { }

    @Get('graph')
    @ApiQuery({ required: false, name: 'startDate', type: String, example: '2025-05-01' })
    @ApiQuery({ required: false, name: 'endDate', type: String, example: '2025-05-30' })
    @ApiQuery({ required: false, name: 'branchId', type: Number, example: 1 })
    findAll(@Query('startDate') startDate?: string, @Query('endDate') endDate?: string, @Query('branchId') branchId?: number) {
        return this.dashboardService.getDashboardGraph(startDate, endDate, +branchId);
    }

}
