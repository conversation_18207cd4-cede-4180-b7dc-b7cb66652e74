import { BadRequestException, Injectable } from '@nestjs/common';
import { Category } from 'src/category/entities/category.entity';
import { QueueItem } from 'src/queue/entities/queue-item.entity';
import { Between } from 'typeorm';

@Injectable()
export class DashboardService {

    async getDashboardGraph(startDate: string, endDate: string, branchId: number) {
        const categories = await Category.find();

        const where: any = {};
        if (startDate && endDate) {
            where.createdAt = Between(new Date(startDate), new Date(endDate));
        } else if (startDate) {
            where.createdAt = Between(new Date(startDate), new Date());
        }

        if (branchId) {
            where.queue = {
                branch: {
                    id: branchId,
                },
            };
        }

        const queueItems = await QueueItem.find({
            where,
            relations: {
                product: {
                    category: true,
                },
                queue: {
                    branch: true,
                },
            },
            select: {
                netWeight: true,
                total: true,
                product: {
                    name: true,
                    category: {
                        id: true,
                        name: true,
                    },
                },
            },
        });

        const categoryTotals: { [key: string]: { totalWeight: number; totalPrice: number } } = {};

        for (const category of categories) {
            categoryTotals[category.name] = {
                totalWeight: 0,
                totalPrice: 0,
            };
        }

        for (const item of queueItems) {
            const categoryName = item.product.category.name;
            const weight = Number(item.netWeight || 0);
            const price = Number(item.total || 0);

            if (categoryTotals[categoryName]) {
                categoryTotals[categoryName].totalWeight += weight;
                categoryTotals[categoryName].totalPrice += price;
            }
        }

        const categoryList = [];
        for (const categoryName in categoryTotals) {
            categoryList.push({
                categoryName: categoryName,
                totalWeight: categoryTotals[categoryName].totalWeight,
                totalPrice: categoryTotals[categoryName].totalPrice,
            });
        }

        return categoryList;
    }

}
