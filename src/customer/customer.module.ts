import { Module } from '@nestjs/common';
import { CustomerService } from './customer.service';
import { CustomerController } from './customer.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Customer } from './entities/customer.entity';
import { LicensePlate } from './entities/license-plate.entity';
import { CustomerBank } from './entities/customer-bank.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Customer, LicensePlate, CustomerBank])
  ],
  controllers: [CustomerController],
  providers: [CustomerService],
})
export class CustomerModule {}
