import { Level } from "../../level/entities/level.entity";
import { CustomBaseEntity } from "../../common/entities";
import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany, OneToOne } from "typeorm";
import { Upload } from "../../upload/entities/upload.entity";
import { Customer } from "./customer.entity";
import { Order } from "../../order/entities/order.entity";
import { Invoice } from "src/invoice/entities/invoice.entity";

@Entity()
export class CustomerBank extends CustomBaseEntity {
    @Column({ name: 'account_name' })
    accountName: string;

    @Column({ name: 'account_number' })
    accountNumber: string;

    @Column({ nullable: true })
    bank: string;

    @ManyToOne(() => Customer, (_) => _.customerBanks)
    @JoinColumn({ name: 'customer_id' })
    customer: Customer;

    @OneToMany(() => Order, (_) => _.customerBank)
    orders: Order[];

    @OneToMany(() => Invoice, (_) => _.customerBank)
    invoices: Invoice[];

    constructor(partial?: Partial<CustomerBank>) {
        super();
        if (partial) {
            Object.assign(this, partial)
        }
    }
}
