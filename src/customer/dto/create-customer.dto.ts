import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";

export class CreateCustomerDto {
  // @IsNotEmpty()
  // readonly code: string;

  @IsNotEmpty()
  readonly name: string;

  // @IsNotEmpty()
  // readonly levelId?: number;

  // @IsNotEmpty()
  // readonly prefixId: number;

  readonly address?: string;

  readonly phoneNumber?: string;

  readonly pointBalance?: number;

  @IsNotEmpty()
  readonly tax: string;

  readonly company?: string;

  readonly identityCardId?: number;

  // readonly licensePlates?: CreateCustomerPlateDto[];

  readonly licensePlates?: string[];

  readonly banks?: CreateCustomerBankDto[]
}

export class CreateCustomerBankDto {
  readonly id: number;
  readonly accountName: string;
  readonly accountNumber: string;
  readonly bank: string;
}

export class CreateCustomerPlateDto {
  readonly id: number;
  readonly licensePlate: string;
}
