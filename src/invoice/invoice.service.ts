import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateInvoiceDto } from './dto/create-invoice.dto';
import { UpdateInvoiceDto } from './dto/update-invoice.dto';
import { Invoice, InvoiceStatus } from './entities/invoice.entity';
import { FilterOperator, PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, DataSource, Repository } from 'typeorm';
import { Order } from 'src/order/entities/order.entity';
import { OrderItem } from 'src/order/entities/order-item.entity';
import { InvoiceItem } from './entities/invoice-item.entity';
import { DateTime } from "luxon";
import { Helper } from 'src/common/helper';

export const INVOICE_PAGINATION_CONFIG: PaginateConfig<Invoice> = {
  sortableColumns: ['id', 'documentDate', 'createdAt'],
  relations: {
    paymentMethod: true,
    user: true,
    customer: true
  },
  // nullSort: 'last',
  defaultSortBy: [['documentDate', 'DESC']],
  filterableColumns: {
  }
};

@Injectable()
export class InvoiceService {
  constructor(
    private dataSource: DataSource,
    @InjectRepository(Invoice)
    private invoiceRepository: Repository<Invoice>,
    @InjectRepository(InvoiceItem)
    private invoiceItemRepository: Repository<InvoiceItem>,
    @InjectRepository(Order)
    private orderRepository: Repository<Order>,
    @InjectRepository(OrderItem)
    private orderItemRepository: Repository<OrderItem>,
  ) { }

  create(createInvoiceDto: CreateInvoiceDto) {
    return 'This action adds a new invoice';
  }

  findAll() {
    return this.invoiceRepository.find();
  }

  async findOne(id: number) {
    const invoice = await this.invoiceRepository.findOne({
      where: { id },
      relations: {
        invoiceItems: {
          product: true
        },
        user: true,
        customer: true,
      }
    })

    if (!invoice) {
      throw new NotFoundException('Invoice not found')
    }

    return invoice
  }

  update(id: number, updateInvoiceDto: UpdateInvoiceDto) {
    return `This action updates a #${id} invoice`;
  }

  async remove(id: number) {
    await this.invoiceRepository.delete(id)
  }

  async datatables(query: PaginateQuery): Promise<Paginated<Invoice>> {
    return paginate(query, this.invoiceRepository, INVOICE_PAGINATION_CONFIG);
  }

  async tranferOrderToInvoice(orderNo: number) {
    const order = await this.orderRepository.findOne({
      where: { orderNo },
      relations: {
        orderItems: true,
        user: true,
        customer: true,
        paymentMethod: true
      }
    })

    if (!order) {
      throw new NotFoundException('order not found')
    }


    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const invoice = this.invoiceRepository.create({
        ...order,
        invoiceNo: await this.generateOrderNum(),
        documentDate: DateTime.now(),
        invoiceStatus: InvoiceStatus.COMPLETE,
      })

      await queryRunner.manager.save(invoice);

      for (const orderItem of order.orderItems) {
        const newOrderItem = this.invoiceItemRepository.create({
          ...orderItem,
          invoice,
        })

        await queryRunner.manager.save(newOrderItem);
      }

      await queryRunner.commitTransaction();

      return this.invoiceRepository.findOne({ where: { id: invoice.id } })
    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException();
    } finally {
      await queryRunner.release();
    }
  }

  private async generateOrderNum(): Promise<number> {

    const now = DateTime.now().setLocale('th-TH')
    const start = now.set({ hour: 0, minute: 0, second: 0 }).toJSDate();
    const end = now.set({ hour: 23, minute: 59, second: 59 }).toJSDate();

    const lastOrder = await this.invoiceRepository.findAndCount({
      where: {
        documentDate: Between(start, end),
      },
      order: {
        documentDate: "DESC",
      },
    });

    let newDocumentNo: string;
    let runNumber = '001'
    const thaidate = Helper.DateToThaiDate(now.toLocaleString({ year: '2-digit', month: '2-digit', day: '2-digit' }))

    if (!lastOrder.length) {
      newDocumentNo = thaidate + runNumber
    } else {
      const newNumber = lastOrder[1] + 1
      runNumber = ('' + newNumber).padStart(3, '0')
    }

    newDocumentNo = thaidate + runNumber

    return +newDocumentNo
  }
}
