import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { Product } from "../../product/entities/product.entity";
import { Column, ManyToOne, JoinColumn, Entity } from "typeorm";
import { Invoice } from "./invoice.entity";
import { CustomBaseEntity } from "../../common/entities";

@Entity()
export class InvoiceItem extends CustomBaseEntity {
  constructor(partial?: Partial<InvoiceItem>) {
    super();
    if (partial) {
      Object.assign(this, partial)
    }
  }

  @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
  quantity: number

  @Column({ type: 'numeric', default: 0, nullable: true, transformer: new DecimalColumnTransformer() })
  dequantity: number

  @Column({ type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer() })
  price: number;

  @Column({ type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer() })
  total: number

  @Column({ nullable: true })
  uptext: string;

  @Column({ nullable: true })
  downtext: string;

  @ManyToOne(() => Product, (_) => _.invoiceItems, { onDelete: 'CASCADE'})
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @ManyToOne(() => Invoice, (_) => _.invoiceItems, { onDelete: 'CASCADE'})
  @JoinColumn({ name: 'invoice_id' })
  invoice: Invoice;
}
