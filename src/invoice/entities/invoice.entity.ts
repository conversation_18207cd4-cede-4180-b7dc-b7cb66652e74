import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { CustomBaseEntity } from "../../common/entities";
import { Customer } from "../../customer/entities/customer.entity";
import { LicensePlate } from "../../customer/entities/license-plate.entity";
import { PaymentMethod } from "../../payment-method/entities/payment-method.entity";
import { User } from "../../user/entities/user.entity";
import { Index, Column, JoinColumn, ManyToOne, Entity, OneToMany } from "typeorm";
import { InvoiceItem } from "./invoice-item.entity";
import { CustomerBank } from "src/customer/entities/customer-bank.entity";

export enum InvoiceStatus {
  SELECT_PAYMENT = "select_payment",
  WAIT_PAYMENT = "wait_payment",
  COMPLETE = "complete",
  INCOMPLETE = "incomplete",
  VOID = "void",
}

@Entity()
export class Invoice extends CustomBaseEntity {
  constructor(partial?: Partial<Invoice>) {
    super();
    if (partial) {
      Object.assign(this, partial)
    }
  }

  @Index()
  @Column({ name: 'invoice_no' })
  invoiceNo: number

  @Column({ name: 'document_date' })
  documentDate: Date;

  @Column({ name: 'invoice_status', type: 'enum', enum: InvoiceStatus })
  invoiceStatus: InvoiceStatus;

  @Column({ type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer() })
  total: number;

  @Column({ type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer() })
  discount: number;

  @Column({ name: 'grand_total', type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer() })
  grandTotal: number;

  @ManyToOne(() => User, (_) => _.invoices)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Customer, (_) => _.invoices)
  @JoinColumn({ name: 'customer_id' })
  customer: Customer;

  @Column({ name: 'license_plate', nullable: true })
  licensePlate: string;

  @ManyToOne(() => PaymentMethod, (_) => _.invoices)
  @JoinColumn({ name: 'payment_method_id' })
  paymentMethod: PaymentMethod;

  @OneToMany(() => InvoiceItem, (_) => _.invoice)
  invoiceItems: InvoiceItem[];

  @ManyToOne(() => CustomerBank, (_) => _.invoices)
  @JoinColumn({ name: 'customer_bank_id' })
  customerBank: CustomerBank;
}
