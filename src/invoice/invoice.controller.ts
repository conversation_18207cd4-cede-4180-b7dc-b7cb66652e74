import { Controller, Get, Post, Body, Param, Delete, Put, HttpCode, HttpStatus } from '@nestjs/common';
import { INVOICE_PAGINATION_CONFIG, InvoiceService } from './invoice.service';
import { CreateInvoiceDto } from './dto/create-invoice.dto';
import { UpdateInvoiceDto } from './dto/update-invoice.dto';
import { ApiTags } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { TranferInvoiceDto } from './dto/tranfer-invoice.dto';

@Controller('invoice')
@ApiTags('ใบกำกับ')
@Auth()
export class InvoiceController {
  constructor(private readonly invoiceService: InvoiceService) {}

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(INVOICE_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.invoiceService.datatables(query);
  }

  @Post('tranfer')
  tranferFromOrder(@Body() dto: TranferInvoiceDto) {
    return this.invoiceService.tranferOrderToInvoice(+dto.orderNo);
  }

  @Post()
  create(@Body() createInvoiceDto: CreateInvoiceDto) {
    return this.invoiceService.create(createInvoiceDto);
  }

  @Get()
  findAll() {
    return this.invoiceService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.invoiceService.findOne(+id);
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() updateInvoiceDto: UpdateInvoiceDto) {
    return this.invoiceService.update(+id, updateInvoiceDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.invoiceService.remove(+id);
  }
}
