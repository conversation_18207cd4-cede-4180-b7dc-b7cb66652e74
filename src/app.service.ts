import { Injectable } from '@nestjs/common';
import { DateTime } from 'luxon';
import { Order, OrderStatus } from './order/entities/order.entity';
import { Between, DataSource } from 'typeorm';
import { Product } from './product/entities/product.entity';
import { OrderItem } from './order/entities/order-item.entity';

@Injectable()
export class AppService {
  constructor(
    private dataSource: DataSource,
  ) { }
  getHello(): string {
    return 'Hello World!';
  }

  async dashboard() {
    const now = DateTime.now();

    const dailyQuery = this.dataSource.getRepository(Order)
      .createQueryBuilder('order')
      .select('SUM(order.grand_total)', 'daily')
      .where("CAST(order.order_date AS DATE) = :date", { date: now.toJSDate() })
      .andWhere("order.order_status = 'select_payment'")
      .getRawOne();

    const startOfWeek = now.startOf('week').toJSDate()
    const endOfWeek = now.endOf('week').toJSDate()

    const weeklyQuery = this.dataSource.getRepository(Order)
      .createQueryBuilder('order')
      .select('SUM(order.grand_total)', 'weekly')
      .where("CAST(order.order_date AS DATE) >= :startDate", { startDate: startOfWeek })
      .andWhere("CAST(order.order_date AS DATE) <= :endDate", { endDate: endOfWeek })
      .andWhere("order.order_status = 'select_payment'")
      .getRawOne();

    const startOfyear = now.startOf('year').toJSDate()
    const endOfYear = now.endOf('year').toJSDate()

    const yearlyQuery = this.dataSource.getRepository(Order)
      .createQueryBuilder('order')
      .select('SUM(order.grand_total)', 'yearly')
      .where("CAST(order.order_date AS DATE) >= :startDate", { startDate: startOfyear })
      .andWhere("CAST(order.order_date AS DATE) <= :endDate", { endDate: endOfYear })
      .andWhere("order.order_status = 'select_payment'")
      .getRawOne();

    const [daily, weekly, yearly] = await Promise.all([dailyQuery, weeklyQuery, yearlyQuery])

    return {
      daily: +(daily['daily'] ?? 0),
      weekly: +(weekly['weekly'] ?? 0),
      yearly: +(yearly['yearly'] ?? 0),
    }
  }

  async top10Product() {
    const startDate: Date = DateTime.now().startOf('month').toJSDate();
    const endDate: Date = DateTime.now().endOf('month').toJSDate();

    const result = await this.dataSource.getRepository(Product)
      .createQueryBuilder('product')
      .select('product.code')
      .addSelect('product.name')
      .addSelect("CAST(COUNT(order_item.product_id) AS integer)", "score")
      .innerJoin(OrderItem, "order_item", "product.id = order_item.product_id")
      .innerJoin(Order, "order", "order_item.order_id = order.id")
      .where("order.order_status = :status", { status: 'select_payment' })
      .andWhere("CAST(order.order_date AS DATE) >= :startDate", { startDate })
      .andWhere("CAST(order.order_date AS DATE) <= :endDate", { endDate })
      .groupBy("product.code")
      .addGroupBy("product.name")
      .orderBy("score", "DESC")
      .limit(10)
      .getRawMany();

    return result
  }

  async last10Orders() {

    const orders = Order.find({
      select: {
        id: true, orderDate: true, orderNo: true, grandTotal: true,
        user: {
          id: true,
          firstName: true,
          lastName: true
        },
        branch: {
          id: true,
          name: true,
        },
        paymentMethod: {
          id: true,
          name: true,
        },
      },
      where: {
        orderStatus: OrderStatus.SELECT_PAYMENT,
      },
      order: { orderDate: 'DESC' },
      take: 10,
      relations: {
        user: true,
        branch: true,
        paymentMethod: true
      }
    })

    return orders
  }

  async yearlyTotal(year: number, branch: number) {
    const queries = [];

    for (let i = 1; i <= 12; i++) {
      const startDate: Date = DateTime.fromObject({ year: year, month: i }).startOf('month').toJSDate();
      const endDate: Date = DateTime.fromObject({ year: year, month: i }).endOf('month').toJSDate();

      // สร้าง query builder
      const queryBuilder = this.dataSource.getRepository(Order)
        .createQueryBuilder('order')
        .select('COALESCE(SUM(order.grand_total), 0)', 'sum')
        .where("CAST(order.order_date AS DATE) >= :startDate", { startDate })
        .andWhere("CAST(order.order_date AS DATE) <= :endDate", { endDate })
        .andWhere("order.order_status = 'select_payment'");

      // ถ้ามีการระบุสาขา (branch ≠ 0) ให้กรองเพิ่ม
      if (branch !== 0) {
        queryBuilder.andWhere("order.branch_id = :branch", { branch });
      }

      queries.push(queryBuilder.getRawOne());
    }

    const result = await Promise.all(queries);

    const data = {};
    for (let i = 1; i <= 12; i++) {
      data[i] = +(result[i - 1]['sum']);
    }

    return data;
  }

  async topSellers(year: number, month: number = 0, limit: number = 10) {
    const startDate = DateTime.fromObject({ year, month: month || 1 }).startOf(month === 0 ? 'year' : 'month').toJSDate();
    const endDate = DateTime.fromObject({ year, month: month || 12 }).endOf(month === 0 ? 'year' : 'month').toJSDate();

    const result = await this.dataSource.getRepository(Order)
      .createQueryBuilder('order')
      .leftJoin('order.user', 'user')
      .select([
        'user.id AS user_id',
        "CONCAT(user.firstName, ' ', user.lastName) AS name",
        'COUNT(order.id) AS order_count',
        'SUM(order.grand_total) AS total_sales',
      ])
      .where("order.order_status = 'select_payment'")
      .andWhere("order.order_date >= :startDate", { startDate })
      .andWhere("order.order_date <= :endDate", { endDate })
      .groupBy('user.id')
      .addGroupBy('user.firstName')
      .addGroupBy('user.lastName')
      .orderBy('total_sales', 'DESC')
      .limit(limit)
      .getRawMany();

    return result.map(row => ({
      user_id: +row.user_id,
      name: row.name,
      order_count: +row.order_count,
      total_sales: +row.total_sales,
    }));
  }

  async topCustomers(year: number, month: number = 0, limit: number = 10) {
    const startDate = DateTime.fromObject({ year, month: month || 1 }).startOf(month === 0 ? 'year' : 'month').toJSDate();
    const endDate = DateTime.fromObject({ year, month: month || 12 }).endOf(month === 0 ? 'year' : 'month').toJSDate();
  
    const result = await this.dataSource.getRepository(Order)
      .createQueryBuilder('order')
      .leftJoin('order.customer', 'customer')
      .select([
        'customer.id AS customer_id',
        "customer.name AS name",  // แก้ชื่อตามจริงใน entity
        'COUNT(order.id) AS order_count',
        'SUM(order.grand_total) AS total_spent',
      ])
      .where("order.order_status = 'select_payment'")
      .andWhere("order.order_date >= :startDate", { startDate })
      .andWhere("order.order_date <= :endDate", { endDate })
      .groupBy('customer.id')
      .addGroupBy('customer.name')
      .orderBy('total_spent', 'DESC')
      .limit(limit)
      .getRawMany();
  
    return result.map(row => ({
      customer_id: +row.customer_id,
      name: row.name,
      order_count: +row.order_count,
      total_spent: +row.total_spent,
    }));
  }
  
  async topSellerDetail(userId: number, year: number, month: number = 0) {
    const startDate = DateTime.fromObject({ year, month: month || 1 }).startOf(month === 0 ? 'year' : 'month').toJSDate();
    const endDate = DateTime.fromObject({ year, month: month || 12 }).endOf(month === 0 ? 'year' : 'month').toJSDate();
  
    const orders = await this.dataSource.getRepository(Order)
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.customer', 'customer')
      .where("order.user_id = :userId", { userId })
      .andWhere("order.order_status = 'select_payment'")
      .andWhere("order.order_date >= :startDate", { startDate })
      .andWhere("order.order_date <= :endDate", { endDate })
      .orderBy('order.order_date', 'DESC')
      .getMany();
  
    return orders.map(order => ({
      orderId: order.id,
      orderNo: order.orderNo,
      orderDate: order.orderDate,
      customerName: order.customer ? `${order.customer.name}` : null,
      grandTotal: +order.grandTotal,
      // branch_id: order.branch_id
    }));
  }

  async topCustomerDetail(customerId: number, year: number, month: number = 0) {
    const startDate = DateTime.fromObject({ year, month: month || 1 }).startOf(month === 0 ? 'year' : 'month').toJSDate();
    const endDate = DateTime.fromObject({ year, month: month || 12 }).endOf(month === 0 ? 'year' : 'month').toJSDate();
  
    const orders = await this.dataSource.getRepository(Order)
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.user', 'user')
      .where("order.customer_id = :customerId", { customerId })
      .andWhere("order.order_status = 'select_payment'")
      .andWhere("order.order_date >= :startDate", { startDate })
      .andWhere("order.order_date <= :endDate", { endDate })
      .orderBy('order.order_date', 'DESC')
      .getMany();
  
    return orders.map(order => ({
      orderId: order.id,
      orderNo: order.orderNo,
      orderDate: order.orderDate,
      sellerName: order.user ? `${order.user.firstName} ${order.user.lastName}` : null,
      grandTotal: +order.grandTotal,
      // branch_id: order.branch_id
    }));
  }
  
}
