import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { WarehouseService } from './warehouse.service';
import { CreateWarehouseDto } from './dto/create-warehouse.dto';
import { UpdateWarehouseDto } from './dto/update-warehouse.dto';
import { Warehouse, WarehouseStatus } from './entities/warehouse.entity';
import { Auth } from '../auth/decorators/auth.decorator';

@ApiTags('Warehouses')
@Controller('warehouses')
@Auth()
export class WarehouseController {
  constructor(private readonly warehouseService: WarehouseService) {}

  @Post()
  @ApiOperation({ summary: 'สร้างคลังสินค้าใหม่' })
  @ApiResponse({ status: 201, description: 'สร้างคลังสินค้าสำเร็จ', type: Warehouse })
  @ApiResponse({ status: 400, description: 'ข้อมูลไม่ถูกต้อง' })
  create(@Body() createWarehouseDto: CreateWarehouseDto): Promise<Warehouse> {
    return this.warehouseService.create(createWarehouseDto);
  }

  @Get()
  @ApiOperation({ summary: 'ดึงรายการคลังสินค้าทั้งหมด' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ', type: [Warehouse] })
  findAll(): Promise<Warehouse[]> {
    return this.warehouseService.findAll();
  }

  @Get('branch/:branchId')
  @ApiOperation({ summary: 'ดึงรายการคลังสินค้าตามสาขา' })
  @ApiParam({ name: 'branchId', description: 'รหัสสาขา' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ', type: [Warehouse] })
  findByBranch(@Param('branchId', ParseIntPipe) branchId: number): Promise<Warehouse[]> {
    return this.warehouseService.findByBranch(branchId);
  }

  @Get('generate-code/:branchId')
  @ApiOperation({ summary: 'สร้างรหัสคลังสินค้าอัตโนมัติ' })
  @ApiParam({ name: 'branchId', description: 'รหัสสาขา' })
  @ApiResponse({ status: 200, description: 'สร้างรหัสสำเร็จ' })
  generateCode(@Param('branchId', ParseIntPipe) branchId: number): Promise<string> {
    return this.warehouseService.generateWarehouseCode(branchId);
  }

  @Get('code/:code')
  @ApiOperation({ summary: 'ดึงข้อมูลคลังสินค้าตามรหัส' })
  @ApiParam({ name: 'code', description: 'รหัสคลังสินค้า' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ', type: Warehouse })
  @ApiResponse({ status: 404, description: 'ไม่พบคลังสินค้า' })
  findByCode(@Param('code') code: string): Promise<Warehouse> {
    return this.warehouseService.findByCode(code);
  }

  @Get(':id')
  @ApiOperation({ summary: 'ดึงข้อมูลคลังสินค้าตาม ID' })
  @ApiParam({ name: 'id', description: 'รหัสคลังสินค้า' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ', type: Warehouse })
  @ApiResponse({ status: 404, description: 'ไม่พบคลังสินค้า' })
  findOne(@Param('id', ParseIntPipe) id: number): Promise<Warehouse> {
    return this.warehouseService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'แก้ไขข้อมูลคลังสินค้า' })
  @ApiParam({ name: 'id', description: 'รหัสคลังสินค้า' })
  @ApiResponse({ status: 200, description: 'แก้ไขข้อมูลสำเร็จ', type: Warehouse })
  @ApiResponse({ status: 404, description: 'ไม่พบคลังสินค้า' })
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateWarehouseDto: UpdateWarehouseDto,
  ): Promise<Warehouse> {
    return this.warehouseService.update(id, updateWarehouseDto);
  }

  @Patch(':id/status')
  @ApiOperation({ summary: 'เปลี่ยนสถานะคลังสินค้า' })
  @ApiParam({ name: 'id', description: 'รหัสคลังสินค้า' })
  @ApiQuery({ name: 'status', enum: WarehouseStatus, description: 'สถานะใหม่' })
  @ApiResponse({ status: 200, description: 'เปลี่ยนสถานะสำเร็จ', type: Warehouse })
  changeStatus(
    @Param('id', ParseIntPipe) id: number,
    @Query('status') status: WarehouseStatus,
  ): Promise<Warehouse> {
    return this.warehouseService.changeStatus(id, status);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'ลบคลังสินค้า' })
  @ApiParam({ name: 'id', description: 'รหัสคลังสินค้า' })
  @ApiResponse({ status: 200, description: 'ลบข้อมูลสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบคลังสินค้า' })
  remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.warehouseService.remove(id);
  }
}
