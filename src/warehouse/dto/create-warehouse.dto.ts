import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsEnum, IsNumber, Min } from 'class-validator';
import { WarehouseStatus } from '../entities/warehouse.entity';

export class CreateWarehouseDto {
    @ApiProperty({ description: 'รหัสคลังสินค้า' })
    @IsString()
    @IsNotEmpty()
    code: string;

    @ApiProperty({ description: 'ชื่อคลังสินค้า' })
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiProperty({ description: 'คำอธิบายคลังสินค้า', required: false })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({ description: 'ที่ตั้งคลังสินค้า', required: false })
    @IsOptional()
    @IsString()
    location?: string;

    @ApiProperty({ 
        description: 'สถานะคลังสินค้า', 
        enum: WarehouseStatus,
        default: WarehouseStatus.ACTIVE,
        required: false 
    })
    @IsOptional()
    @IsEnum(WarehouseStatus)
    status?: WarehouseStatus;

    @ApiProperty({ description: 'ความจุสูงสุด (ตัน)', required: false })
    @IsOptional()
    @IsNumber()
    @Min(0)
    capacity?: number;

    @ApiProperty({ description: 'พื้นที่ (ตารางเมตร)', required: false })
    @IsOptional()
    @IsNumber()
    @Min(0)
    area?: number;

    @ApiProperty({ description: 'ผู้จัดการคลังสินค้า', required: false })
    @IsOptional()
    @IsString()
    manager?: string;

    @ApiProperty({ description: 'เบอร์ติดต่อ', required: false })
    @IsOptional()
    @IsString()
    contact?: string;

    @ApiProperty({ description: 'รหัสสาขา' })
    @IsNumber()
    @IsNotEmpty()
    branchId: number;
}
