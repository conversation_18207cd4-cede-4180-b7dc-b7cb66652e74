export class Helper {
  static isToday(date: Date): boolean {
    const today = new Date();

    return date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear();
  }

  static generateNumber(lastNumber: number | null): number {
    // Get current date in GMT+7 timezone
    const currentDate: Date = new Date();
    const gmtPlus7Time: number = currentDate.getTime() + (7 * 60 * 60 * 1000); // Adjust for GMT+7
    const gmtPlus7Date: string = new Date(gmtPlus7Time).toISOString().slice(0, 10);

    // If it's a new day or no last number provided, start from 1, else use provided last number + 1
    const currentNumber: number = lastNumber === null || gmtPlus7Date !== lastNumber.toString().slice(0, 8)
      ? 1
      : lastNumber + 1;

    // Pad the number with leading zeros
    const formattedNumber: string = currentNumber.toString().padStart(4, '0');

    // Construct the 12-digit number
    const number: number = parseInt(`${gmtPlus7Date.replace(/-/g, '')}${formattedNumber}`);
    return number - 100000000000;
  }

  static DateToThaiDate(date: string) {
    const listDate = date.split('/');

    return listDate[2] + listDate[1] + listDate[0]
  }
}

export function imagePath(path: string) {
  return process.env.APP_URL + '/' + path;
}