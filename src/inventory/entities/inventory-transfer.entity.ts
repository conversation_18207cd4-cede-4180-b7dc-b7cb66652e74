import { Column, Entity, Index, ManyToOne, OneToMany } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { Branch } from "../../branch/entities/branch.entity";
import { Warehouse } from "../../warehouse/entities/warehouse.entity";
import { User } from "../../user/entities/user.entity";
import { InventoryTransferItem } from "./inventory-transfer-item.entity";

export enum TransferStatus {
    DRAFT = "draft",           // ร่าง
    PENDING = "pending",       // รออนุมัติ
    APPROVED = "approved",     // อนุมัติแล้ว
    IN_TRANSIT = "in_transit", // กำลังขนส่ง
    COMPLETED = "completed",   // เสร็จสิ้น
    CANCELLED = "cancelled"    // ยกเลิก
}

@Entity()
export class InventoryTransfer extends CustomBaseEntity {
    @Index()
    @Column({ unique: true })
    transferNumber: string; // เลขที่โอน

    @Column()
    transferDate: Date; // วันที่โอน

    @Column({ type: 'enum', enum: TransferStatus, default: TransferStatus.DRAFT })
    status: TransferStatus;

    @ManyToOne(() => Branch)
    fromBranch: Branch; // สาขาต้นทาง

    @ManyToOne(() => Warehouse)
    fromWarehouse: Warehouse; // คลังต้นทาง

    @ManyToOne(() => Branch)
    toBranch: Branch; // สาขาปลายทาง

    @ManyToOne(() => Warehouse)
    toWarehouse: Warehouse; // คลังปลายทาง

    @Column({ type: 'text', nullable: true })
    reason: string; // เหตุผลในการโอน

    @Column({ type: 'text', nullable: true })
    notes: string; // หมายเหตุ

    @Column({ nullable: true })
    expectedDate: Date; // วันที่คาดว่าจะได้รับ

    @Column({ nullable: true })
    actualDate: Date; // วันที่ได้รับจริง

    @ManyToOne(() => User)
    requestedBy: User; // ผู้ขอโอน

    @ManyToOne(() => User, { nullable: true })
    approvedBy: User; // ผู้อนุมัติ

    @Column({ nullable: true })
    approvedAt: Date; // วันที่อนุมัติ

    @ManyToOne(() => User, { nullable: true })
    receivedBy: User; // ผู้รับ

    @Column({ nullable: true })
    receivedAt: Date; // วันที่รับ

    @OneToMany(() => InventoryTransferItem, (item) => item.transfer, { cascade: true })
    items: InventoryTransferItem[];

    constructor(partial?: Partial<InventoryTransfer>) {
        super();
        if (partial) {
            Object.assign(this, partial);
        }
    }
}
