import { Column, Entity, ManyToOne } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { Product } from "../../product/entities/product.entity";
import { InventoryTransfer } from "./inventory-transfer.entity";

@Entity()
export class InventoryTransferItem extends CustomBaseEntity {
    @ManyToOne(() => InventoryTransfer, (transfer) => transfer.items, { onDelete: 'CASCADE' })
    transfer: InventoryTransfer;

    @ManyToOne(() => Product)
    product: Product;

    @Column()
    productId: number;

    @Column()
    productName: string;

    @Column()
    productCode: string;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    requestedQuantity: number; // จำนวนที่ขอโอน

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    transferredQuantity: number; // จำนวนที่โอนจริง

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    receivedQuantity: number; // จำนวนที่ได้รับ

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), nullable: true })
    unitCost: number; // ต้นทุนต่อหน่วย

    @Column({ type: 'text', nullable: true })
    notes: string; // หมายเหตุ

    constructor(partial?: Partial<InventoryTransferItem>) {
        super();
        if (partial) {
            Object.assign(this, partial);
        }
    }
}
