import { Column, Entity, Index, ManyToOne, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { Product } from "../../product/entities/product.entity";
import { Branch } from "../../branch/entities/branch.entity";
import { Warehouse } from "../../warehouse/entities/warehouse.entity";
import { InventoryTransaction } from "./inventory-transaction.entity";

@Entity()
@Unique(['product', 'branch', 'warehouse'])
export class Inventory extends CustomBaseEntity {
    @ManyToOne(() => Product, (product) => product.inventories)
    product: Product;

    @ManyToOne(() => Branch, (branch) => branch.inventories)
    branch: Branch;

    @ManyToOne(() => Warehouse, (warehouse) => warehouse.inventories)
    warehouse: Warehouse;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    quantity: number; // จำนวนคงเหลือ

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    reservedQuantity: number; // จำนวนที่จอง

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    availableQuantity: number; // จำนวนที่พร้อมใช้ (quantity - reservedQuantity)

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    minStock: number; // จำนวนขั้นต่ำ

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    maxStock: number; // จำนวนสูงสุด

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    reorderPoint: number; // จุดสั่งซื้อใหม่

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    averageCost: number; // ต้นทุนเฉลี่ย

    @Column({ nullable: true })
    lastUpdatedBy: number; // ผู้อัปเดตล่าสุด

    @Column({ nullable: true })
    lastTransactionDate: Date; // วันที่ทำรายการล่าสุด

    @Column({ type: 'text', nullable: true })
    notes: string; // หมายเหตุ

    @OneToMany(() => InventoryTransaction, (transaction) => transaction.inventory)
    transactions: InventoryTransaction[];

    constructor(partial?: Partial<Inventory>) {
        super();
        if (partial) {
            Object.assign(this, partial);
        }
    }
}
