import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InventoryService } from './inventory.service';
import { InventoryTransferService } from './inventory-transfer.service';
import { InventoryController } from './inventory.controller';
import { InventoryTransferController } from './inventory-transfer.controller';
import { Inventory } from './entities/inventory.entity';
import { InventoryTransaction } from './entities/inventory-transaction.entity';
import { InventoryTransfer } from './entities/inventory-transfer.entity';
import { InventoryTransferItem } from './entities/inventory-transfer-item.entity';
import { ProductModule } from '../product/product.module';
import { BranchModule } from '../branch/branch.module';
import { WarehouseModule } from '../warehouse/warehouse.module';
import { UserModule } from '../user/user.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Inventory,
      InventoryTransaction,
      InventoryTransfer,
      InventoryTransferItem,
    ]),
    ProductModule,
    BranchModule,
    WarehouseModule,
    UserModule,
  ],
  controllers: [InventoryController, InventoryTransferController],
  providers: [InventoryService, InventoryTransferService],
  exports: [InventoryService, InventoryTransferService],
})
export class InventoryModule {}
