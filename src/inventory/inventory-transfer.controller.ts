import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { InventoryTransferService } from './inventory-transfer.service';
import { CreateInventoryTransferDto } from './dto/create-inventory-transfer.dto';
import { UpdateInventoryTransferDto } from './dto/update-inventory-transfer.dto';
import { InventoryTransfer } from './entities/inventory-transfer.entity';
import { Auth } from '../auth/decorators/auth.decorator';

@ApiTags('Inventory Transfers')
@Controller('inventory-transfers')
@Auth()
export class InventoryTransferController {
  constructor(private readonly transferService: InventoryTransferService) {}

  @Post()
  @ApiOperation({ summary: 'สร้างใบโอนสินค้าใหม่' })
  @ApiResponse({ status: 201, description: 'สร้างใบโอนสำเร็จ', type: InventoryTransfer })
  @ApiResponse({ status: 400, description: 'ข้อมูลไม่ถูกต้อง' })
  create(
    @Body() createTransferDto: CreateInventoryTransferDto,
    @Request() req: any,
  ): Promise<InventoryTransfer> {
    return this.transferService.create(createTransferDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: 'ดึงรายการใบโอนสินค้าทั้งหมด' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ', type: [InventoryTransfer] })
  findAll(): Promise<InventoryTransfer[]> {
    return this.transferService.findAll();
  }

  @Get('branch/:branchId')
  @ApiOperation({ summary: 'ดึงรายการใบโอนสินค้าตามสาขา' })
  @ApiParam({ name: 'branchId', description: 'รหัสสาขา' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ', type: [InventoryTransfer] })
  findByBranch(@Param('branchId', ParseIntPipe) branchId: number): Promise<InventoryTransfer[]> {
    return this.transferService.findByBranch(branchId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'ดึงข้อมูลใบโอนสินค้าตาม ID' })
  @ApiParam({ name: 'id', description: 'รหัสใบโอน' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ', type: InventoryTransfer })
  @ApiResponse({ status: 404, description: 'ไม่พบใบโอน' })
  findOne(@Param('id', ParseIntPipe) id: number): Promise<InventoryTransfer> {
    return this.transferService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'แก้ไขข้อมูลใบโอนสินค้า' })
  @ApiParam({ name: 'id', description: 'รหัสใบโอน' })
  @ApiResponse({ status: 200, description: 'แก้ไขข้อมูลสำเร็จ', type: InventoryTransfer })
  @ApiResponse({ status: 404, description: 'ไม่พบใบโอน' })
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateTransferDto: UpdateInventoryTransferDto,
  ): Promise<InventoryTransfer> {
    return this.transferService.update(id, updateTransferDto);
  }

  @Post(':id/approve')
  @ApiOperation({ summary: 'อนุมัติใบโอนสินค้า' })
  @ApiParam({ name: 'id', description: 'รหัสใบโอน' })
  @ApiResponse({ status: 200, description: 'อนุมัติสำเร็จ', type: InventoryTransfer })
  @ApiResponse({ status: 400, description: 'ไม่สามารถอนุมัติได้' })
  approve(
    @Param('id', ParseIntPipe) id: number,
    @Request() req: any,
  ): Promise<InventoryTransfer> {
    return this.transferService.approve(id, req.user.id);
  }

  @Post(':id/ship')
  @ApiOperation({ summary: 'จัดส่งสินค้า (หักสต็อกจากต้นทาง)' })
  @ApiParam({ name: 'id', description: 'รหัสใบโอน' })
  @ApiResponse({ status: 200, description: 'จัดส่งสำเร็จ', type: InventoryTransfer })
  @ApiResponse({ status: 400, description: 'ไม่สามารถจัดส่งได้' })
  ship(@Param('id', ParseIntPipe) id: number): Promise<InventoryTransfer> {
    return this.transferService.ship(id);
  }

  @Post(':id/receive')
  @ApiOperation({ summary: 'รับสินค้า (เพิ่มสต็อกที่ปลายทาง)' })
  @ApiParam({ name: 'id', description: 'รหัสใบโอน' })
  @ApiBody({
    description: 'รายการสินค้าที่รับ',
    schema: {
      type: 'object',
      properties: {
        receivedItems: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              itemId: { type: 'number', description: 'รหัสรายการสินค้า' },
              receivedQuantity: { type: 'number', description: 'จำนวนที่รับ' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 200, description: 'รับสินค้าสำเร็จ', type: InventoryTransfer })
  @ApiResponse({ status: 400, description: 'ไม่สามารถรับสินค้าได้' })
  receive(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: { receivedItems: { itemId: number; receivedQuantity: number }[] },
    @Request() req: any,
  ): Promise<InventoryTransfer> {
    return this.transferService.receive(id, req.user.id, body.receivedItems);
  }

  @Post(':id/cancel')
  @ApiOperation({ summary: 'ยกเลิกใบโอนสินค้า' })
  @ApiParam({ name: 'id', description: 'รหัสใบโอน' })
  @ApiResponse({ status: 200, description: 'ยกเลิกสำเร็จ', type: InventoryTransfer })
  @ApiResponse({ status: 400, description: 'ไม่สามารถยกเลิกได้' })
  cancel(@Param('id', ParseIntPipe) id: number): Promise<InventoryTransfer> {
    return this.transferService.cancel(id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'ลบใบโอนสินค้า' })
  @ApiParam({ name: 'id', description: 'รหัสใบโอน' })
  @ApiResponse({ status: 200, description: 'ลบข้อมูลสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบโอน' })
  remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.transferService.remove(id);
  }
}
