import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsNotEmpty, IsOptional, Min } from 'class-validator';

export class CreateInventoryDto {
    @ApiProperty({ description: 'รหัสสินค้า' })
    @IsNumber()
    @IsNotEmpty()
    productId: number;

    @ApiProperty({ description: 'รหัสสาขา' })
    @IsNumber()
    @IsNotEmpty()
    branchId: number;

    @ApiProperty({ description: 'รหัสคลังสินค้า' })
    @IsNumber()
    @IsNotEmpty()
    warehouseId: number;

    @ApiProperty({ description: 'จำนวนเริ่มต้น', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    quantity?: number;

    @ApiProperty({ description: 'จำนวนขั้นต่ำ', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    minStock?: number;

    @ApiProperty({ description: 'จำนวนสูงสุด', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    maxStock?: number;

    @ApiProperty({ description: 'จุดสั่งซื้อใหม่', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    reorderPoint?: number;

    @ApiProperty({ description: 'ต้นทุนเฉลี่ย', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    averageCost?: number;

    @ApiProperty({ description: 'หมายเหตุ', required: false })
    @IsOptional()
    notes?: string;
}
