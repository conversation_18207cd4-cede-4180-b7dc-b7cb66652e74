import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsNotEmpty, IsOptional, IsString, IsArray, ValidateNested, IsDateString } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateInventoryTransferItemDto {
    @ApiProperty({ description: 'รหัสสินค้า' })
    @IsNumber()
    @IsNotEmpty()
    productId: number;

    @ApiProperty({ description: 'ชื่อสินค้า' })
    @IsString()
    @IsNotEmpty()
    productName: string;

    @ApiProperty({ description: 'รหัสสินค้า' })
    @IsString()
    @IsNotEmpty()
    productCode: string;

    @ApiProperty({ description: 'จำนวนที่ขอโอน' })
    @IsNumber()
    @IsNotEmpty()
    requestedQuantity: number;

    @ApiProperty({ description: 'ต้นทุนต่อหน่วย', required: false })
    @IsOptional()
    @IsNumber()
    unitCost?: number;

    @ApiProperty({ description: 'หมายเหตุ', required: false })
    @IsOptional()
    @IsString()
    notes?: string;
}

export class CreateInventoryTransferDto {
    @ApiProperty({ description: 'วันที่โอน' })
    @IsDateString()
    transferDate: string;

    @ApiProperty({ description: 'รหัสสาขาต้นทาง' })
    @IsNumber()
    @IsNotEmpty()
    fromBranchId: number;

    @ApiProperty({ description: 'รหัสคลังต้นทาง' })
    @IsNumber()
    @IsNotEmpty()
    fromWarehouseId: number;

    @ApiProperty({ description: 'รหัสสาขาปลายทาง' })
    @IsNumber()
    @IsNotEmpty()
    toBranchId: number;

    @ApiProperty({ description: 'รหัสคลังปลายทาง' })
    @IsNumber()
    @IsNotEmpty()
    toWarehouseId: number;

    @ApiProperty({ description: 'เหตุผลในการโอน', required: false })
    @IsOptional()
    @IsString()
    reason?: string;

    @ApiProperty({ description: 'หมายเหตุ', required: false })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiProperty({ description: 'วันที่คาดว่าจะได้รับ', required: false })
    @IsOptional()
    @IsDateString()
    expectedDate?: string;

    @ApiProperty({ description: 'รายการสินค้าที่โอน', type: [CreateInventoryTransferItemDto] })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CreateInventoryTransferItemDto)
    items: CreateInventoryTransferItemDto[];
}
