import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { InventoryTransfer, TransferStatus } from './entities/inventory-transfer.entity';
import { InventoryTransferItem } from './entities/inventory-transfer-item.entity';
import { CreateInventoryTransferDto } from './dto/create-inventory-transfer.dto';
import { UpdateInventoryTransferDto } from './dto/update-inventory-transfer.dto';
import { InventoryService } from './inventory.service';
import { TransactionType } from './entities/inventory-transaction.entity';

@Injectable()
export class InventoryTransferService {
  constructor(
    @InjectRepository(InventoryTransfer)
    private transferRepository: Repository<InventoryTransfer>,
    @InjectRepository(InventoryTransferItem)
    private transferItemRepository: Repository<InventoryTransferItem>,
    private inventoryService: InventoryService,
    private dataSource: DataSource,
  ) {}

  async create(createTransferDto: CreateInventoryTransferDto, userId: number): Promise<InventoryTransfer> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // สร้างเลขที่โอน
      const transferNumber = await this.generateTransferNumber();

      // สร้าง transfer
      const transfer = queryRunner.manager.create(InventoryTransfer, {
        transferNumber,
        transferDate: new Date(createTransferDto.transferDate),
        status: TransferStatus.DRAFT,
        fromBranch: { id: createTransferDto.fromBranchId } as any,
        fromWarehouse: { id: createTransferDto.fromWarehouseId } as any,
        toBranch: { id: createTransferDto.toBranchId } as any,
        toWarehouse: { id: createTransferDto.toWarehouseId } as any,
        reason: createTransferDto.reason,
        notes: createTransferDto.notes,
        expectedDate: createTransferDto.expectedDate ? new Date(createTransferDto.expectedDate) : null,
        requestedBy: { id: userId } as any
      });

      const savedTransfer = await queryRunner.manager.save(transfer);

      // สร้าง transfer items
      for (const itemDto of createTransferDto.items) {
        const transferItem = queryRunner.manager.create(InventoryTransferItem, {
          transfer: savedTransfer,
          product: { id: itemDto.productId } as any,
          productId: itemDto.productId,
          productName: itemDto.productName,
          productCode: itemDto.productCode,
          requestedQuantity: itemDto.requestedQuantity,
          transferredQuantity: 0,
          receivedQuantity: 0,
          unitCost: itemDto.unitCost,
          notes: itemDto.notes
        });

        await queryRunner.manager.save(transferItem);
      }

      await queryRunner.commitTransaction();
      return this.findOne(savedTransfer.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async findAll(): Promise<InventoryTransfer[]> {
    return this.transferRepository.find({
      relations: {
        fromBranch: true,
        fromWarehouse: true,
        toBranch: true,
        toWarehouse: true,
        requestedBy: true,
        approvedBy: true,
        receivedBy: true,
        items: {
          product: true
        }
      },
      order: {
        createdAt: 'DESC'
      }
    });
  }

  async findByBranch(branchId: number): Promise<InventoryTransfer[]> {
    return this.transferRepository.find({
      where: [
        { fromBranch: { id: branchId } },
        { toBranch: { id: branchId } }
      ],
      relations: {
        fromBranch: true,
        fromWarehouse: true,
        toBranch: true,
        toWarehouse: true,
        requestedBy: true,
        approvedBy: true,
        receivedBy: true,
        items: {
          product: true
        }
      },
      order: {
        createdAt: 'DESC'
      }
    });
  }

  async findOne(id: number): Promise<InventoryTransfer> {
    const transfer = await this.transferRepository.findOne({
      where: { id },
      relations: {
        fromBranch: true,
        fromWarehouse: true,
        toBranch: true,
        toWarehouse: true,
        requestedBy: true,
        approvedBy: true,
        receivedBy: true,
        items: {
          product: {
            category: true,
            unit: true
          }
        }
      }
    });

    if (!transfer) {
      throw new NotFoundException(`Inventory transfer with ID ${id} not found`);
    }

    return transfer;
  }

  async update(id: number, updateTransferDto: UpdateInventoryTransferDto): Promise<InventoryTransfer> {
    const transfer = await this.findOne(id);

    if (transfer.status !== TransferStatus.DRAFT) {
      throw new BadRequestException('Can only update draft transfers');
    }

    const updateData: any = { ...updateTransferDto };
    
    // แปลง foreign key fields
    if (updateTransferDto.fromBranchId) {
      updateData.fromBranch = { id: updateTransferDto.fromBranchId };
      delete updateData.fromBranchId;
    }
    if (updateTransferDto.fromWarehouseId) {
      updateData.fromWarehouse = { id: updateTransferDto.fromWarehouseId };
      delete updateData.fromWarehouseId;
    }
    if (updateTransferDto.toBranchId) {
      updateData.toBranch = { id: updateTransferDto.toBranchId };
      delete updateData.toBranchId;
    }
    if (updateTransferDto.toWarehouseId) {
      updateData.toWarehouse = { id: updateTransferDto.toWarehouseId };
      delete updateData.toWarehouseId;
    }

    // ลบ items ออกจาก updateData เพราะต้องจัดการแยก
    delete updateData.items;

    await this.transferRepository.update(id, updateData);
    return this.findOne(id);
  }

  async approve(id: number, userId: number): Promise<InventoryTransfer> {
    const transfer = await this.findOne(id);

    if (transfer.status !== TransferStatus.DRAFT && transfer.status !== TransferStatus.PENDING) {
      throw new BadRequestException('Transfer must be in draft or pending status to approve');
    }

    await this.transferRepository.update(id, {
      status: TransferStatus.APPROVED,
      approvedBy: { id: userId } as any,
      approvedAt: new Date()
    });

    return this.findOne(id);
  }

  async ship(id: number): Promise<InventoryTransfer> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const transfer = await queryRunner.manager.findOne(InventoryTransfer, {
        where: { id },
        relations: { items: true }
      });

      if (!transfer) {
        throw new NotFoundException(`Transfer with ID ${id} not found`);
      }

      if (transfer.status !== TransferStatus.APPROVED) {
        throw new BadRequestException('Transfer must be approved before shipping');
      }

      // ตรวจสอบและหัก stock จาก source
      for (const item of transfer.items) {
        try {
          const inventory = await this.inventoryService.findByProductAndLocation(
            item.productId,
            transfer.fromBranch.id,
            transfer.fromWarehouse.id
          );

          if (inventory.availableQuantity < item.requestedQuantity) {
            throw new BadRequestException(`Insufficient stock for product ${item.productName}`);
          }

          // หัก stock จาก source
          await this.inventoryService.createTransaction({
            inventoryId: inventory.id,
            type: TransactionType.TRANSFER_OUT,
            quantity: -item.requestedQuantity,
            unitCost: item.unitCost,
            description: `Transfer out to ${transfer.toBranch.name} - ${transfer.toWarehouse.name}`,
            referenceType: 'inventory_transfer',
            referenceId: transfer.id,
            referenceNumber: transfer.transferNumber
          }, transfer.requestedBy.id);

          // อัปเดต transferred quantity
          await queryRunner.manager.update(InventoryTransferItem, item.id, {
            transferredQuantity: item.requestedQuantity
          });

        } catch (error) {
          if (error instanceof NotFoundException) {
            throw new BadRequestException(`Product ${item.productName} not found in source location`);
          }
          throw error;
        }
      }

      // อัปเดตสถานะ
      await queryRunner.manager.update(InventoryTransfer, id, {
        status: TransferStatus.IN_TRANSIT
      });

      await queryRunner.commitTransaction();
      return this.findOne(id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async receive(id: number, userId: number, receivedItems: { itemId: number; receivedQuantity: number }[]): Promise<InventoryTransfer> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const transfer = await queryRunner.manager.findOne(InventoryTransfer, {
        where: { id },
        relations: { items: true, fromBranch: true, fromWarehouse: true, toBranch: true, toWarehouse: true }
      });

      if (!transfer) {
        throw new NotFoundException(`Transfer with ID ${id} not found`);
      }

      if (transfer.status !== TransferStatus.IN_TRANSIT) {
        throw new BadRequestException('Transfer must be in transit to receive');
      }

      // รับสินค้าและเพิ่ม stock ที่ destination
      for (const receivedItem of receivedItems) {
        const transferItem = transfer.items.find(item => item.id === receivedItem.itemId);
        if (!transferItem) {
          throw new BadRequestException(`Transfer item with ID ${receivedItem.itemId} not found`);
        }

        if (receivedItem.receivedQuantity > transferItem.transferredQuantity) {
          throw new BadRequestException(`Received quantity cannot exceed transferred quantity for ${transferItem.productName}`);
        }

        // หา inventory ที่ destination หรือสร้างใหม่
        let destinationInventory;
        try {
          destinationInventory = await this.inventoryService.findByProductAndLocation(
            transferItem.productId,
            transfer.toBranch.id,
            transfer.toWarehouse.id
          );
        } catch (error) {
          if (error instanceof NotFoundException) {
            // สร้าง inventory ใหม่ที่ destination
            destinationInventory = await this.inventoryService.create({
              productId: transferItem.productId,
              branchId: transfer.toBranch.id,
              warehouseId: transfer.toWarehouse.id,
              quantity: 0
            });
          } else {
            throw error;
          }
        }

        // เพิ่ม stock ที่ destination
        await this.inventoryService.createTransaction({
          inventoryId: destinationInventory.id,
          type: TransactionType.TRANSFER_IN,
          quantity: receivedItem.receivedQuantity,
          unitCost: transferItem.unitCost,
          description: `Transfer in from ${transfer.fromBranch.name} - ${transfer.fromWarehouse.name}`,
          referenceType: 'inventory_transfer',
          referenceId: transfer.id,
          referenceNumber: transfer.transferNumber
        }, userId);

        // อัปเดต received quantity
        await queryRunner.manager.update(InventoryTransferItem, transferItem.id, {
          receivedQuantity: receivedItem.receivedQuantity
        });
      }

      // อัปเดตสถานะ
      await queryRunner.manager.update(InventoryTransfer, id, {
        status: TransferStatus.COMPLETED,
        receivedBy: { id: userId } as any,
        receivedAt: new Date(),
        actualDate: new Date()
      });

      await queryRunner.commitTransaction();
      return this.findOne(id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async cancel(id: number): Promise<InventoryTransfer> {
    const transfer = await this.findOne(id);

    if (transfer.status === TransferStatus.COMPLETED) {
      throw new BadRequestException('Cannot cancel completed transfer');
    }

    await this.transferRepository.update(id, {
      status: TransferStatus.CANCELLED
    });

    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    const transfer = await this.findOne(id);

    if (transfer.status !== TransferStatus.DRAFT && transfer.status !== TransferStatus.CANCELLED) {
      throw new BadRequestException('Can only delete draft or cancelled transfers');
    }

    await this.transferRepository.softDelete(id);
  }

  private async generateTransferNumber(): Promise<string> {
    const now = new Date();
    const year = now.getFullYear().toString().slice(-2);
    const month = (now.getMonth() + 1).toString().padStart(2, '0');

    // หา transfer ล่าสุดในเดือนนี้
    const lastTransfer = await this.transferRepository
      .createQueryBuilder('transfer')
      .where('transfer.transferNumber LIKE :pattern', { pattern: `TR${year}${month}%` })
      .orderBy('transfer.transferNumber', 'DESC')
      .getOne();

    let sequence = 1;
    if (lastTransfer) {
      const lastSequence = parseInt(lastTransfer.transferNumber.slice(-4));
      sequence = lastSequence + 1;
    }

    return `TR${year}${month}${sequence.toString().padStart(4, '0')}`;
  }
}
