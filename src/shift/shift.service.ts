import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateShiftDto } from './dto/create-shift.dto';
import { UpdateShiftDto } from './dto/update-shift.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Shift, ShiftStatus } from './entities/shift.entity';

@Injectable()
export class ShiftService {
  constructor(
    @InjectRepository(Shift)
    private shiftRepository: Repository<Shift>,
  ) { }

  create(userId: number, createShiftDto: CreateShiftDto) {
    const shift = this.shiftRepository.create({
      ...createShiftDto,
      status: ShiftStatus.OPEN,
      user: {
        id: userId,
      }
    });

    return this.shiftRepository.save(shift);
  }

  findAll() {
    return this.shiftRepository.find();
  }

  async findOne(id: number) {
    const shift = await this.shiftRepository.findOneBy({ id });

    if (!shift) {
      throw new NotFoundException(`Shift ID ${id} not found`)
    }

    return shift;
  }

  update(id: number, updateShiftDto: UpdateShiftDto) {
    return this.shiftRepository.save({
      id: id,
      ...updateShiftDto
    });
  }

  async remove(id: number) {
    await this.shiftRepository.delete(id);
  }
}
