import { Controller, Get, Post, Body, Param, Delete, Req, Put } from '@nestjs/common';
import { ShiftService } from './shift.service';
import { CreateShiftDto } from './dto/create-shift.dto';
import { UpdateShiftDto } from './dto/update-shift.dto';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { Auth } from 'src/auth/decorators/auth.decorator';

@Controller('shift')
@ApiTags('กะทำงาน')
@Auth()
export class ShiftController {
  constructor(private readonly shiftService: ShiftService) { }

  @Post()
  create(@Req() req: Request, @Body() createShiftDto: CreateShiftDto) {
    const userId = req.user['sub'];

    return this.shiftService.create(userId, createShiftDto);
  }

  @Get()
  findAll() {
    return this.shiftService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.shiftService.findOne(+id);
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() updateShiftDto: UpdateShiftDto) {
    return this.shiftService.update(+id, updateShiftDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.shiftService.remove(+id);
  }
}
