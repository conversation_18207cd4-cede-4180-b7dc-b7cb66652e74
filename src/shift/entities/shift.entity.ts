import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, OneToMany } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Order } from "../../order/entities/order.entity";
import { User } from "../../user/entities/user.entity";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { Branch } from "../../branch/entities/branch.entity";

export enum ShiftStatus {
  OPEN = 'open',
  CLOSED = 'closed',
}

@Entity()
export class Shift extends CustomBaseEntity {
  @Column({ name: 'change', type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer() })
  change: number;

  @Column({ name: 'cash', type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer() })
  cash: number;

  @Column({ nullable: true })
  remark: string;

  @Column({ type: 'enum', enum: ShiftStatus })
  status: ShiftStatus

  @OneToMany(() => Order, (_) => _.shift)
  orders: Order[];

  @ManyToOne(() => User, (_) => _.shifts)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Branch, (_) => _.shifts)
  @JoinColumn({ name: 'branch_id' })
  branch: Branch;

}