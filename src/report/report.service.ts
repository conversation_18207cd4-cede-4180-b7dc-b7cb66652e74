import { Injectable, NotFoundException } from '@nestjs/common';
import { Workbook } from 'exceljs';
import * as puppeteer from 'puppeteer';
import { OrderService } from 'src/order/order.service';
import { CreateReportDto } from './dto/create-report.dto';
import { UpdateReportDto } from './dto/update-report.dto';
import { DateTime } from 'luxon';
import { bahttext } from "bahttext"
import { imagePath } from 'src/common/helper';
import { InjectRepository } from '@nestjs/typeorm';
import { RewardReport } from '../reward/entities/rewardreport.entity';
import { Repository } from 'typeorm';
import { Buffer } from 'buffer';

@Injectable()
export class ReportService {
  constructor(
    private orderService: OrderService,
    @InjectRepository(RewardReport)
    private readonly rewardReportRepository: Repository<RewardReport>,
  ) {

  }

  create(createReportDto: CreateReportDto) {
    return 'This action adds a new report';
  }

  findAll() {
    return `This action returns all report`;
  }

  findOne(id: number) {
    return `This action returns a #${id} report`;
  }

  update(id: number, updateReportDto: UpdateReportDto) {
    return `This action updates a #${id} report`;
  }

  remove(id: number) {
    return `This action removes a #${id} report`;
  }

  async reportOrderExcel(startDate: Date, endDate: Date) {
    const workbook = new Workbook

    const worksheet = workbook.addWorksheet('order')

    worksheet.columns = [
      { header: '', key: '' },
      { header: 'Order No', key: 'order_no' },
      { header: 'Order Date', key: 'order_date' },
      { header: 'Total', key: 'grand_total' },
      { header: 'Branch', key: 'branch' },
      { header: 'Cashier', key: 'cashier' },
    ]

    const content = await this.orderService.report(startDate, endDate);

    let data = content.map((e, i, _) => ({
      no: "" + i + 1,
      order_no: e.orderNo,
      order_date: e.orderDate.toLocaleDateString('th-TH'),
      total: "" + e.grandTotal,
      branch: e?.shift?.branch?.name ?? '-',
      cashier: e?.shift?.user?.fullName ?? '-',
    }));

    data.forEach((val, i, _) => {
      console.log(val);
      worksheet.addRow(val)
    })

    // const buffer = await workbook.xlsx.writeBuffer()
    return workbook.xlsx.writeBuffer();
  }

  // async reportOrderPdf() {
  //   const pdfBuffer: Buffer = await new Promise(resolve => {
  //     const doc = new PDFDocument({
  //       size: 'A4',
  //       bufferPages: true,
  //     })


  //     doc.font('THSarabunNew/THSarabunNew.ttf');
  //     // customize your PDF document
  //     doc.text('บริษัท ลิ้มกอบกุล จำกัด', { align: 'center' })
  //     doc.text('148/6 ถ.สมุทรสงคราม-บางแพ ต.แม่กลอง อ.เมืองฯ จ.สมุทรสงคราม 75000', { align: 'center' })
  //     doc.text('ใบสำคัญจ่ายเงิน', { align: 'center' })
  //     doc.moveDown()
  //     doc.text('วันที่')
  //     doc.text('จ่ายให้แก่')
  //     doc.text('เลขบัตรประชาชน')
  //     doc.text('ที่อยู่')
  //     doc.text('ทะเบียน')

  //     doc.text('ผู้รับเงิน', { width: 100 })
  //       .text('ผู้จ่ายเงิน', { width: 100 })

  //     doc.end()

  //     const buffer = []
  //     doc.on('data', buffer.push.bind(buffer))
  //     doc.on('end', () => {
  //       const data = Buffer.concat(buffer)
  //       resolve(data)
  //     })
  //   })

  //   return pdfBuffer
  // }

  async reportOrderPdf1(orderId: number) {
    const order = await this.orderService.findOne(orderId);
    if (!order) {
      throw new NotFoundException(`Order not found.`)
    }

    const date = DateTime.fromJSDate(order.orderDate);
    const dateStr = date.setLocale('th-TH').toLocaleString(DateTime.DATE_MED);

    let orderItems = ''
    let lastIndex = 0
    for (let index = 0; index < order?.orderItems.length; index++) {
      lastIndex = index
      const orderItem = order?.orderItems[index];

      orderItems += `<tr>
                        <td style="border: 1px solid black; text-align: center; width: 10%;">${index + 1}</td>
                        <td style="border: 1px solid black; width: 50%;">${orderItem?.product?.name}</td>
                        <td style="border: 1px solid black; text-align: center; width: 10%;">${orderItem?.quantity}</td>
                        <td style="border: 1px solid black; text-align: center; width: 10%;">${orderItem?.price}</td>
                        <td style="border: 1px solid black; text-align: center; width: 20%;">${orderItem?.total}</td>
                    </tr>`
    }

    for (let index = lastIndex + 1; index < 8 - order?.orderItems.length; index++) {
      orderItems += `<tr>
                        <td style="border: 1px solid black;text-align: center;width: 10%;">${index + 1}</td>
                        <td style="border: 1px solid black;width: 50%;"> </td>
                        <td style="border: 1px solid black;width: 10%;"> </td>
                        <td style="border: 1px solid black;width: 10%;"> </td>
                        <td style="border: 1px solid black;width: 20%;"> </td>
                    </tr>`
    }

    let identityCard = ''
    if (order?.customer?.identityCard) {
      identityCard = `
      <div style="display: flex; justify-content: center;">
        ข้าพเจ้าได้รับชำระค่าสินค้าตามรายละเอียดข้างต้นไว้ถูกต้องและครบถ้วนแล้ว
      </div>
      <br>
      <div style="display: flex; justify-content: center;">
        <img src="${imagePath(order?.customer?.identityCard?.filename)}" width="250px" height="auto">
      </div>`
    }

    let checked = '<svg width="16px" height="16px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <!-- Uploaded to: SVG Repo, www.svgrepo.com, Generator: SVG Repo Mixer Tools --> <title>ic_fluent_checkbox_unchecked_24_regular</title> <desc>Created with Sketch.</desc> <g id="🔍-Product-Icons" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"> <g id="ic_fluent_checkbox_unchecked_24_regular" fill="#212121" fill-rule="nonzero"> <path d="M5.75,3 L18.25,3 C19.7687831,3 21,4.23121694 21,5.75 L21,18.25 C21,19.7687831 19.7687831,21 18.25,21 L5.75,21 C4.23121694,21 3,19.7687831 3,18.25 L3,5.75 C3,4.23121694 4.23121694,3 5.75,3 Z M5.75,4.5 C5.05964406,4.5 4.5,5.05964406 4.5,5.75 L4.5,18.25 C4.5,18.9403559 5.05964406,19.5 5.75,19.5 L18.25,19.5 C18.9403559,19.5 19.5,18.9403559 19.5,18.25 L19.5,5.75 C19.5,5.05964406 18.9403559,4.5 18.25,4.5 L5.75,4.5 Z" id="🎨Color"> </path> </g> </g> </g></svg>'
    if (order?.paymentMethod.type == 'cash') {
      checked = '<svg width="16px" height="16px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#000000" stroke="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <!-- Uploaded to: SVG Repo, www.svgrepo.com, Generator: SVG Repo Mixer Tools --> <title>ic_fluent_checkbox_checked_24_regular</title> <desc>Created with Sketch.</desc> <g id="🔍-Product-Icons" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"> <g id="ic_fluent_checkbox_checked_24_regular" fill="#212121" fill-rule="nonzero"> <path d="M18.25,3 C19.7687831,3 21,4.23121694 21,5.75 L21,18.25 C21,19.7687831 19.7687831,21 18.25,21 L5.75,21 C4.23121694,21 3,19.7687831 3,18.25 L3,5.75 C3,4.23121694 4.23121694,3 5.75,3 L18.25,3 Z M18.25,4.5 L5.75,4.5 C5.05964406,4.5 4.5,5.05964406 4.5,5.75 L4.5,18.25 C4.5,18.9403559 5.05964406,19.5 5.75,19.5 L18.25,19.5 C18.9403559,19.5 19.5,18.9403559 19.5,18.25 L19.5,5.75 C19.5,5.05964406 18.9403559,4.5 18.25,4.5 Z M10,14.4393398 L16.4696699,7.96966991 C16.7625631,7.6767767 17.2374369,7.6767767 17.5303301,7.96966991 C17.7965966,8.23593648 17.8208027,8.65260016 17.6029482,8.94621165 L17.5303301,9.03033009 L10.5303301,16.0303301 C10.2640635,16.2965966 9.84739984,16.3208027 9.55378835,16.1029482 L9.46966991,16.0303301 L6.46966991,13.0303301 C6.1767767,12.7374369 6.1767767,12.2625631 6.46966991,11.9696699 C6.73593648,11.7034034 7.15260016,11.6791973 7.44621165,11.8970518 L7.53033009,11.9696699 L10,14.4393398 L16.4696699,7.96966991 L10,14.4393398 Z" id="🎨Color"> </path> </g> </g> </g></svg>'
    }

    const browser = await puppeteer.launch({
      args: [
        '--no-sandbox',
        '--disable-web-security'
      ]
    });

    const page = await browser.newPage();

    const content = `
    <!DOCTYPE html>
    <html lang="th">
    
    <head>
        <meta charset="UTF-8">
        <title>Document</title>
        <link href="https://fonts.cdnfonts.com/css/th-sarabun-new-4" rel="stylesheet">
        <style>
          input[type="checkbox"] {
            background-color: white;
          }
        </style>
    </head>
    
    <body style="
          padding: 2cm;
          margin: auto;
          background: white;
          font-family: 'TH Sarabun New', sans-serif;">
        <div style="display: flex;justify-content: end;">เลขที่ ${order.orderNo}</div>
        <div style="text-align: center; font-size: 22px;"><b>บริษัท ลิ้มกอบกุล จำกัด</b></div>
        <div style="text-align: center; font-size: 18px;">148/6 ถ.สมุทรสงคราม-บางแพ ต.แม่กลอง อ.เมืองฯ จ.สมุทรสงคราม 75000
        </div>
        <br>
        <div style="text-align: center; font-size: 20px;"><b>ใบสำคัญจ่ายเงิน</b></div>
        <br>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="width: 80%; text-align: right;">วันที่</td>
                <td style="border-bottom: 1px solid black; text-align: center;">${dateStr}</td>
            </tr>
        </table>
        <br>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="width: 20%;">จ่ายให้แก่</td>
                <td style="border-bottom: 1px solid black; width: 30%;">${order?.customer?.name ?? '-'}</td>
                <td style="width: 20%;">เลขบัตรประชาชน</td>
                <td style="border-bottom: 1px solid black; width: 30%;">${order?.customer?.tax ?? '-'}</td>
            </tr>
            <tr>
                <td>ที่อยู่</td>
                <td colspan="3" style="border-bottom: 1px solid black;">${order?.customer?.address ?? '-'}</td>
            </tr>
            <tr>
                <td>ทะเบียน</td>
                <td style="border-bottom: 1px solid black;">${order?.licensePlate ?? '-'}</td>
            </tr>
        </table>
        <br><br>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <th style="border: 1px solid black;">ลำดับ</th>
                <th style="border: 1px solid black;">รายการ</th>
                <th style="border: 1px solid black;">จำนวน</th>
                <th style="border: 1px solid black;">หน่วยละ</th>
                <th style="border: 1px solid black;">จำนวนเงิน</th>
            </tr>
            ${orderItems}
            <tr>
                <td colspan="2" style="text-align: center; ">(${bahttext(order?.grandTotal)})</td>
                <td colspan="2" style="text-align: center; border: 1px solid black;">รวม</td>
                <td style="text-align: center; border: 1px solid black;">${order?.grandTotal}</td>
            </tr>
        </table>
        
        <br>
        <div>${order?.customerBank?.accountNumber ?? ''} ${order?.customerBank?.accountName ?? ''} ${order?.customerBank?.bank ?? ''}</div>
        <br>
        <div>จ่ายชำระโดย</div>
        <div style="display: flex;align-items: center;gap: 4px;">${checked} ${order?.paymentMethod?.name}</div>
        <br><br>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style=" text-align: center;">............................................</td>
                <td style=" width: 40%;"></td>
                <td style=" text-align: center; ">............................................</td>
            </tr>
            <tr>
                <td style=" text-align: center;">(${order?.customer?.name ?? ''})
                </td>
                <td style=" width: 40%;"></td>
                <td style=" text-align: center;">(............................................)</td>
            </tr>
            <tr>
                <td style=" text-align: center;">ผู้รับเงิน</td>
                <td style=" width: 40%;"></td>
                <td style=" text-align: center;">ผู้จ่ายเงิน</td>
            </tr>
        </table>
        <br>
        ${identityCard}
    </body>
    
    </html>
`

    // const html = readFileSync(content, 'utf-8');

    await page.setContent(content, { waitUntil: 'networkidle2' });

    const pdfBuffer = await page.pdf({ format: 'A4' });
    // const doc = new jsPDF({
    //   orientation: 'p',
    //   unit: 'mm',
    //   format: 'A4'
    // });

    // doc.text("Hello world!", 1, 1);
    // doc.save()
    // return doc.output('blob');
    await browser.close();

    return pdfBuffer
  }

  async reportOrderFakePdf1(orderId: number) {
    const order = await this.orderService.findOne(orderId);
    if (!order) {
      throw new NotFoundException(`Order not found.`)
    }

    const date = DateTime.fromJSDate(order.orderDate);
    const dateStr = date.setLocale('th-TH').toLocaleString(DateTime.DATE_MED);

    let orderItems = ''
    let lastIndex = 0
    let sumtt = 0;
    for (let index = 0; index < order?.orderItems.length; index++) {
      lastIndex = index
      const orderItem = order?.orderItems[index];

      orderItems += `<tr>
                        <td style="border: 1px solid black; text-align: center; width: 10%;">${index + 1}</td>
                        <td style="border: 1px solid black; width: 50%;">${orderItem?.product?.name}</td>
                        <td style="border: 1px solid black; text-align: center; width: 10%;">${orderItem?.quantity}</td>
                        <td style="border: 1px solid black; text-align: center; width: 10%;">${orderItem?.price}</td>
                        <td style="border: 1px solid black; text-align: center; width: 20%;">${orderItem?.total}</td>
                    </tr>`
                    sumtt+=orderItem?.total;
    }

    for (let index = lastIndex + 1; index < 8 - order?.orderItems.length; index++) {
      orderItems += `<tr>
                        <td style="border: 1px solid black;text-align: center;width: 10%;">${index + 1}</td>
                        <td style="border: 1px solid black;width: 50%;"> </td>
                        <td style="border: 1px solid black;width: 10%;"> </td>
                        <td style="border: 1px solid black;width: 10%;"> </td>
                        <td style="border: 1px solid black;width: 20%;"> </td>
                    </tr>`
                    
    }

    let identityCard = ''
    if (order?.customer?.identityCard) {
      identityCard = `
      <div style="display: flex; justify-content: center;">
        ข้าพเจ้าได้รับชำระค่าสินค้าตามรายละเอียดข้างต้นไว้ถูกต้องและครบถ้วนแล้ว
      </div>
      <br>
      <div style="display: flex; justify-content: center;">
        <img src="${imagePath(order?.customer?.identityCard?.filename)}" width="250px" height="auto">
      </div>`
    }

    let checked = '<svg width="16px" height="16px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <!-- Uploaded to: SVG Repo, www.svgrepo.com, Generator: SVG Repo Mixer Tools --> <title>ic_fluent_checkbox_unchecked_24_regular</title> <desc>Created with Sketch.</desc> <g id="🔍-Product-Icons" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"> <g id="ic_fluent_checkbox_unchecked_24_regular" fill="#212121" fill-rule="nonzero"> <path d="M5.75,3 L18.25,3 C19.7687831,3 21,4.23121694 21,5.75 L21,18.25 C21,19.7687831 19.7687831,21 18.25,21 L5.75,21 C4.23121694,21 3,19.7687831 3,18.25 L3,5.75 C3,4.23121694 4.23121694,3 5.75,3 Z M5.75,4.5 C5.05964406,4.5 4.5,5.05964406 4.5,5.75 L4.5,18.25 C4.5,18.9403559 5.05964406,19.5 5.75,19.5 L18.25,19.5 C18.9403559,19.5 19.5,18.9403559 19.5,18.25 L19.5,5.75 C19.5,5.05964406 18.9403559,4.5 18.25,4.5 L5.75,4.5 Z" id="🎨Color"> </path> </g> </g> </g></svg>'
    if (order?.paymentMethod?.type == 'cash') {
      checked = '<svg width="16px" height="16px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#000000" stroke="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <!-- Uploaded to: SVG Repo, www.svgrepo.com, Generator: SVG Repo Mixer Tools --> <title>ic_fluent_checkbox_checked_24_regular</title> <desc>Created with Sketch.</desc> <g id="🔍-Product-Icons" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"> <g id="ic_fluent_checkbox_checked_24_regular" fill="#212121" fill-rule="nonzero"> <path d="M18.25,3 C19.7687831,3 21,4.23121694 21,5.75 L21,18.25 C21,19.7687831 19.7687831,21 18.25,21 L5.75,21 C4.23121694,21 3,19.7687831 3,18.25 L3,5.75 C3,4.23121694 4.23121694,3 5.75,3 L18.25,3 Z M18.25,4.5 L5.75,4.5 C5.05964406,4.5 4.5,5.05964406 4.5,5.75 L4.5,18.25 C4.5,18.9403559 5.05964406,19.5 5.75,19.5 L18.25,19.5 C18.9403559,19.5 19.5,18.9403559 19.5,18.25 L19.5,5.75 C19.5,5.05964406 18.9403559,4.5 18.25,4.5 Z M10,14.4393398 L16.4696699,7.96966991 C16.7625631,7.6767767 17.2374369,7.6767767 17.5303301,7.96966991 C17.7965966,8.23593648 17.8208027,8.65260016 17.6029482,8.94621165 L17.5303301,9.03033009 L10.5303301,16.0303301 C10.2640635,16.2965966 9.84739984,16.3208027 9.55378835,16.1029482 L9.46966991,16.0303301 L6.46966991,13.0303301 C6.1767767,12.7374369 6.1767767,12.2625631 6.46966991,11.9696699 C6.73593648,11.7034034 7.15260016,11.6791973 7.44621165,11.8970518 L7.53033009,11.9696699 L10,14.4393398 L16.4696699,7.96966991 L10,14.4393398 Z" id="🎨Color"> </path> </g> </g> </g></svg>'
    }

    const browser = await puppeteer.launch({
      args: [
        '--no-sandbox',
        '--disable-web-security'
      ]
    });

    const page = await browser.newPage();

    const content = `
    <!DOCTYPE html>
    <html lang="th">
    
    <head>
        <meta charset="UTF-8">
        <title>Document</title>
        <link href="https://fonts.cdnfonts.com/css/th-sarabun-new-4" rel="stylesheet">
        <style>
          input[type="checkbox"] {
            background-color: white;
          }
        </style>
    </head>
    
    <body style="
          padding: 2cm;
          margin: auto;
          background: white;
          font-family: 'TH Sarabun New', sans-serif;">
        <div style="display: flex;justify-content: end;">เลขที่ ${order.orderNo}</div>
        <div style="text-align: center; font-size: 22px;"><b>บริษัท ลิ้มกอบกุล จำกัด</b></div>
        <div style="text-align: center; font-size: 18px;">148/6 ถ.สมุทรสงคราม-บางแพ ต.แม่กลอง อ.เมืองฯ จ.สมุทรสงคราม 75000
        </div>
        <br>
        <div style="text-align: center; font-size: 20px;"><b>ใบสำคัญจ่ายเงิน</b></div>
        <br>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="width: 80%; text-align: right;">วันที่</td>
                <td style="border-bottom: 1px solid black; text-align: center;">${dateStr}</td>
            </tr>
        </table>
        <br>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="width: 20%;">จ่ายให้แก่</td>
                <td style="border-bottom: 1px solid black; width: 30%;">${order?.customer?.name ?? '-'}</td>
                <td style="width: 20%;">เลขบัตรประชาชน</td>
                <td style="border-bottom: 1px solid black; width: 30%;">${order?.customer?.tax ?? '-'}</td>
            </tr>
            <tr>
                <td>ที่อยู่</td>
                <td colspan="3" style="border-bottom: 1px solid black;">${order?.customer?.address ?? '-'}</td>
            </tr>
            <tr>
                <td>ทะเบียน</td>
                <td style="border-bottom: 1px solid black;">${order?.licensePlate ?? '-'}</td>
            </tr>
        </table>
        <br><br>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <th style="border: 1px solid black;">ลำดับ</th>
                <th style="border: 1px solid black;">รายการ</th>
                <th style="border: 1px solid black;">จำนวน</th>
                <th style="border: 1px solid black;">หน่วยละ</th>
                <th style="border: 1px solid black;">จำนวนเงิน</th>
            </tr>
            ${orderItems}
            <tr>
                <td colspan="2" style="text-align: center; ">(${bahttext(sumtt)})</td>
                <td colspan="2" style="text-align: center; border: 1px solid black;">รวม</td>
                <td style="text-align: center; border: 1px solid black;">${sumtt}</td>
            </tr>
        </table>
        
        <br>
        <div>${order?.customerBank?.accountNumber ?? ''} ${order?.customerBank?.accountName ?? ''} ${order?.customerBank?.bank ?? ''}</div>
        <br>
        <div>จ่ายชำระโดย</div>
        <div style="display: flex;align-items: center;gap: 4px;">${checked} ${order?.paymentMethod?.name}</div>
        <br><br>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style=" text-align: center;">............................................</td>
                <td style=" width: 40%;"></td>
                <td style=" text-align: center; ">............................................</td>
            </tr>
            <tr>
                <td style=" text-align: center;">(${order?.customer?.name ?? ''})
                </td>
                <td style=" width: 40%;"></td>
                <td style=" text-align: center;">(............................................)</td>
            </tr>
            <tr>
                <td style=" text-align: center;">ผู้รับเงิน</td>
                <td style=" width: 40%;"></td>
                <td style=" text-align: center;">ผู้จ่ายเงิน</td>
            </tr>
        </table>
        <br>
       
    </body>
    
    </html>
`

    // const html = readFileSync(content, 'utf-8');

    await page.setContent(content, { waitUntil: 'networkidle2' });

    const pdfBuffer = await page.pdf({ format: 'A4' });
    // const doc = new jsPDF({
    //   orientation: 'p',
    //   unit: 'mm',
    //   format: 'A4'
    // });

    // doc.text("Hello world!", 1, 1);
    // doc.save()
    // return doc.output('blob');
    await browser.close();

    return pdfBuffer
  }

  async reportOrderPrePdf1(orderId: number) {
    const order = await this.orderService.findOne(orderId);
    if (!order) {
      throw new NotFoundException(`Order not found.`)
    }

    const date = DateTime.fromJSDate(order.orderDate);
    const dateStr = date.setLocale('th-TH').toLocaleString(DateTime.DATE_MED);

    let orderItems = ''
    let lastIndex = 0
    for (let index = 0; index < order?.orderItems.length; index++) {
      lastIndex = index
      const orderItem = order?.orderItems[index];

      orderItems += `<tr>
                        <td style="border: 1px solid black; text-align: center; width: 10%;">${index + 1}</td>
                        <td style="border: 1px solid black; width: 50%;">${orderItem?.product?.name}</td>
                        <td style="border: 1px solid black; text-align: center; width: 10%;">${orderItem?.quantity}</td>
                        <td style="border: 1px solid black; text-align: center; width: 10%;">${orderItem?.price}</td>
                        <td style="border: 1px solid black; text-align: center; width: 20%;">${orderItem?.total}</td>
                    </tr>`
    }

    for (let index = lastIndex + 1; index < 8 - order?.orderItems.length; index++) {
      orderItems += `<tr>
                        <td style="border: 1px solid black;text-align: center;width: 10%;">${index + 1}</td>
                        <td style="border: 1px solid black;width: 50%;"> </td>
                        <td style="border: 1px solid black;width: 10%;"> </td>
                        <td style="border: 1px solid black;width: 10%;"> </td>
                        <td style="border: 1px solid black;width: 20%;"> </td>
                    </tr>`
    }

    let identityCard = ''
    if (order?.customer?.identityCard) {
      identityCard = `
      <div style="display: flex; justify-content: center;">
        ข้าพเจ้าได้รับชำระค่าสินค้าตามรายละเอียดข้างต้นไว้ถูกต้องและครบถ้วนแล้ว
      </div>
      <br>
      <div style="display: flex; justify-content: center;">
        <img src="${imagePath(order?.customer?.identityCard?.filename)}" width="250px" height="auto">
      </div>`
    }

    let checked = '<svg width="16px" height="16px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <!-- Uploaded to: SVG Repo, www.svgrepo.com, Generator: SVG Repo Mixer Tools --> <title>ic_fluent_checkbox_unchecked_24_regular</title> <desc>Created with Sketch.</desc> <g id="🔍-Product-Icons" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"> <g id="ic_fluent_checkbox_unchecked_24_regular" fill="#212121" fill-rule="nonzero"> <path d="M5.75,3 L18.25,3 C19.7687831,3 21,4.23121694 21,5.75 L21,18.25 C21,19.7687831 19.7687831,21 18.25,21 L5.75,21 C4.23121694,21 3,19.7687831 3,18.25 L3,5.75 C3,4.23121694 4.23121694,3 5.75,3 Z M5.75,4.5 C5.05964406,4.5 4.5,5.05964406 4.5,5.75 L4.5,18.25 C4.5,18.9403559 5.05964406,19.5 5.75,19.5 L18.25,19.5 C18.9403559,19.5 19.5,18.9403559 19.5,18.25 L19.5,5.75 C19.5,5.05964406 18.9403559,4.5 18.25,4.5 L5.75,4.5 Z" id="🎨Color"> </path> </g> </g> </g></svg>'
    if (order?.paymentMethod.type == 'cash') {
      checked = '<svg width="16px" height="16px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#000000" stroke="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <!-- Uploaded to: SVG Repo, www.svgrepo.com, Generator: SVG Repo Mixer Tools --> <title>ic_fluent_checkbox_checked_24_regular</title> <desc>Created with Sketch.</desc> <g id="🔍-Product-Icons" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"> <g id="ic_fluent_checkbox_checked_24_regular" fill="#212121" fill-rule="nonzero"> <path d="M18.25,3 C19.7687831,3 21,4.23121694 21,5.75 L21,18.25 C21,19.7687831 19.7687831,21 18.25,21 L5.75,21 C4.23121694,21 3,19.7687831 3,18.25 L3,5.75 C3,4.23121694 4.23121694,3 5.75,3 L18.25,3 Z M18.25,4.5 L5.75,4.5 C5.05964406,4.5 4.5,5.05964406 4.5,5.75 L4.5,18.25 C4.5,18.9403559 5.05964406,19.5 5.75,19.5 L18.25,19.5 C18.9403559,19.5 19.5,18.9403559 19.5,18.25 L19.5,5.75 C19.5,5.05964406 18.9403559,4.5 18.25,4.5 Z M10,14.4393398 L16.4696699,7.96966991 C16.7625631,7.6767767 17.2374369,7.6767767 17.5303301,7.96966991 C17.7965966,8.23593648 17.8208027,8.65260016 17.6029482,8.94621165 L17.5303301,9.03033009 L10.5303301,16.0303301 C10.2640635,16.2965966 9.84739984,16.3208027 9.55378835,16.1029482 L9.46966991,16.0303301 L6.46966991,13.0303301 C6.1767767,12.7374369 6.1767767,12.2625631 6.46966991,11.9696699 C6.73593648,11.7034034 7.15260016,11.6791973 7.44621165,11.8970518 L7.53033009,11.9696699 L10,14.4393398 L16.4696699,7.96966991 L10,14.4393398 Z" id="🎨Color"> </path> </g> </g> </g></svg>'
    }

    const browser = await puppeteer.launch({
      args: [
        '--no-sandbox',
        '--disable-web-security'
      ]
    });

    const page = await browser.newPage();

    const content = `
    <!DOCTYPE html>
    <html lang="th">
    
    <head>
        <meta charset="UTF-8">
        <title>Document</title>
        <link href="https://fonts.cdnfonts.com/css/th-sarabun-new-4" rel="stylesheet">
        <style>
          input[type="checkbox"] {
            background-color: white;
          }
        </style>
    </head>
    
    <body style="
          padding: 2cm;
          margin: auto;
          background: white;
          font-family: 'TH Sarabun New', sans-serif;">
        <div style="display: flex;justify-content: end; visibility: hidden;">เลขที่ ${order.orderNo}</div>
        <div style="text-align: center; font-size: 22px;"><b>บริษัท ลิ้มกอบกุล จำกัด</b></div>
        <div style="text-align: center; font-size: 18px;">148/6 ถ.สมุทรสงคราม-บางแพ ต.แม่กลอง อ.เมืองฯ จ.สมุทรสงคราม 75000
        </div>
        <br>
        <div style="text-align: center; font-size: 20px;"><b>ใบสำคัญจ่ายเงิน</b></div>
        <br>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="width: 80%; text-align: right;">วันที่</td>
                <td style="border-bottom: 1px solid black; text-align: center;">${dateStr}</td>
            </tr>
        </table>
        <br>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="width: 20%;">จ่ายให้แก่</td>
                <td style="border-bottom: 1px solid black; width: 30%;">${order?.customer?.name ?? '-'}</td>
                <td style="width: 20%;">เลขบัตรประชาชน</td>
                <td style="border-bottom: 1px solid black; width: 30%;">${order?.customer?.tax ?? '-'}</td>
            </tr>
            <tr>
                <td>ที่อยู่</td>
                <td colspan="3" style="border-bottom: 1px solid black;">${order?.customer?.address ?? '-'}</td>
            </tr>
            <tr>
                <td>ทะเบียน</td>
                <td style="border-bottom: 1px solid black;">${order?.licensePlate ?? '-'}</td>
            </tr>
        </table>
        <br><br>
        <table style="width: 100%; border-collapse: collapse; visibility: hidden;">
            <tr>
                <th style="border: 1px solid black;">ลำดับ</th>
                <th style="border: 1px solid black;">รายการ</th>
                <th style="border: 1px solid black;">จำนวน</th>
                <th style="border: 1px solid black;">หน่วยละ</th>
                <th style="border: 1px solid black;">จำนวนเงิน</th>
            </tr>
            ${orderItems}
            <tr>
                <td colspan="2" style="text-align: center; ">(${bahttext(order?.grandTotal)})</td>
                <td colspan="2" style="text-align: center; border: 1px solid black;">รวม</td>
                <td style="text-align: center; border: 1px solid black;">${order?.grandTotal}</td>
            </tr>
        </table>
        
        <br>
        <div>${order?.customerBank?.accountNumber ?? ''} ${order?.customerBank?.accountName ?? ''} ${order?.customerBank?.bank ?? ''}</div>
        <br>
        <div>จ่ายชำระโดย</div>
        <div style="display: flex;align-items: center;gap: 4px;">${checked} ${order?.paymentMethod?.name}</div>
        <br><br>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style=" text-align: center;">............................................</td>
                <td style=" width: 40%;"></td>
                <td style=" text-align: center; ">............................................</td>
            </tr>
            <tr>
                <td style=" text-align: center;">(${order?.customer?.name ?? ''})
                </td>
                <td style=" width: 40%;"></td>
                <td style=" text-align: center;">(............................................)</td>
            </tr>
            <tr>
                <td style=" text-align: center;">ผู้รับเงิน</td>
                <td style=" width: 40%;"></td>
                <td style=" text-align: center;">ผู้จ่ายเงิน</td>
            </tr>
        </table>
        <br>
        ${identityCard}
    </body>
    
    </html>
`

    // const html = readFileSync(content, 'utf-8');

    await page.setContent(content, { waitUntil: 'networkidle2' });

    const pdfBuffer = await page.pdf({ format: 'A4' });
    // const doc = new jsPDF({
    //   orientation: 'p',
    //   unit: 'mm',
    //   format: 'A4'
    // });

    // doc.text("Hello world!", 1, 1);
    // doc.save()
    // return doc.output('blob');
    await browser.close();

    return pdfBuffer
  }

  async reportOrderPostPdf1(orderId: number, body: any) {
    const order = await this.orderService.findOne(orderId);
    if (!order) {
      throw new NotFoundException(`Order not found.`)
    }

    const date = DateTime.fromJSDate(order.orderDate);
    const dateStr = date.setLocale('th-TH').toLocaleString(DateTime.DATE_MED);

    const products: any[] = body?.products

    let orderItems = ''
    let lastIndex = 0
    for (let index = 0; index < products.length; index++) {
      lastIndex = index
      const product = products[index];

      orderItems += `<tr>
                        <td style="border: 1px solid black; text-align: center; width: 10%;">${index + 1}</td>
                        <td style="border: 1px solid black; width: 50%;">${product?.product_name}</td>
                        <td style="border: 1px solid black; text-align: center; width: 10%;">${product?.qty}</td>
                        <td style="border: 1px solid black; text-align: center; width: 10%;">${product?.unit}</td>
                        <td style="border: 1px solid black; text-align: center; width: 20%;">${product?.total}</td>
                    </tr>`
    }

    for (let index = lastIndex + 1; index < 8 - products.length; index++) {
      orderItems += `<tr>
                        <td style="border: 1px solid black;text-align: center;width: 10%;">${index + 1}</td>
                        <td style="border: 1px solid black;width: 50%;"> </td>
                        <td style="border: 1px solid black;width: 10%;"> </td>
                        <td style="border: 1px solid black;width: 10%;"> </td>
                        <td style="border: 1px solid black;width: 20%;"> </td>
                    </tr>`
    }

    let identityCard = ''
    // if (order?.customer?.identityCard) {
    //   identityCard = `
    //   <div style="display: flex; justify-content: center;">
    //     ข้าพเจ้าได้รับชำระค่าสินค้าตามรายละเอียดข้างต้นไว้ถูกต้องและครบถ้วนแล้ว
    //   </div>
    //   <br>
    //   <div style="display: flex; justify-content: center;">
    //     <img src="${imagePath(order?.customer?.identityCard?.filename)}" width="250px" height="auto">
    //   </div>`
    // }

    let checked = '<svg width="16px" height="16px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <!-- Uploaded to: SVG Repo, www.svgrepo.com, Generator: SVG Repo Mixer Tools --> <title>ic_fluent_checkbox_unchecked_24_regular</title> <desc>Created with Sketch.</desc> <g id="🔍-Product-Icons" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"> <g id="ic_fluent_checkbox_unchecked_24_regular" fill="#212121" fill-rule="nonzero"> <path d="M5.75,3 L18.25,3 C19.7687831,3 21,4.23121694 21,5.75 L21,18.25 C21,19.7687831 19.7687831,21 18.25,21 L5.75,21 C4.23121694,21 3,19.7687831 3,18.25 L3,5.75 C3,4.23121694 4.23121694,3 5.75,3 Z M5.75,4.5 C5.05964406,4.5 4.5,5.05964406 4.5,5.75 L4.5,18.25 C4.5,18.9403559 5.05964406,19.5 5.75,19.5 L18.25,19.5 C18.9403559,19.5 19.5,18.9403559 19.5,18.25 L19.5,5.75 C19.5,5.05964406 18.9403559,4.5 18.25,4.5 L5.75,4.5 Z" id="🎨Color"> </path> </g> </g> </g></svg>'
    if (order?.paymentMethod.type == 'cash') {
      checked = '<svg width="16px" height="16px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#000000" stroke="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <!-- Uploaded to: SVG Repo, www.svgrepo.com, Generator: SVG Repo Mixer Tools --> <title>ic_fluent_checkbox_checked_24_regular</title> <desc>Created with Sketch.</desc> <g id="🔍-Product-Icons" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"> <g id="ic_fluent_checkbox_checked_24_regular" fill="#212121" fill-rule="nonzero"> <path d="M18.25,3 C19.7687831,3 21,4.23121694 21,5.75 L21,18.25 C21,19.7687831 19.7687831,21 18.25,21 L5.75,21 C4.23121694,21 3,19.7687831 3,18.25 L3,5.75 C3,4.23121694 4.23121694,3 5.75,3 L18.25,3 Z M18.25,4.5 L5.75,4.5 C5.05964406,4.5 4.5,5.05964406 4.5,5.75 L4.5,18.25 C4.5,18.9403559 5.05964406,19.5 5.75,19.5 L18.25,19.5 C18.9403559,19.5 19.5,18.9403559 19.5,18.25 L19.5,5.75 C19.5,5.05964406 18.9403559,4.5 18.25,4.5 Z M10,14.4393398 L16.4696699,7.96966991 C16.7625631,7.6767767 17.2374369,7.6767767 17.5303301,7.96966991 C17.7965966,8.23593648 17.8208027,8.65260016 17.6029482,8.94621165 L17.5303301,9.03033009 L10.5303301,16.0303301 C10.2640635,16.2965966 9.84739984,16.3208027 9.55378835,16.1029482 L9.46966991,16.0303301 L6.46966991,13.0303301 C6.1767767,12.7374369 6.1767767,12.2625631 6.46966991,11.9696699 C6.73593648,11.7034034 7.15260016,11.6791973 7.44621165,11.8970518 L7.53033009,11.9696699 L10,14.4393398 L16.4696699,7.96966991 L10,14.4393398 Z" id="🎨Color"> </path> </g> </g> </g></svg>'
    }

    const browser = await puppeteer.launch({
      args: [
        '--no-sandbox',
        '--disable-web-security'
      ]
    });

    const page = await browser.newPage();

    const content = `
    <!DOCTYPE html>
    <html lang="th">
    
    <head>
        <meta charset="UTF-8">
        <title>Document</title>
        <link href="https://fonts.cdnfonts.com/css/th-sarabun-new-4" rel="stylesheet">
    </head>
    
    <body style="
          padding: 2cm;
          margin: auto;
          background: white;
          font-family: 'TH Sarabun New', sans-serif;
          visibility: hidden;">
        <div style="display: flex;justify-content: end;">เลขที่ ${order.orderNo}</div>
        <div>
        <div style="text-align: center; font-size: 22px;"><b>บริษัท ลิ้มกอบกุล จำกัด</b></div>
        <div style="text-align: center; font-size: 18px;">148/6 ถ.สมุทรสงคราม-บางแพ ต.แม่กลอง อ.เมืองฯ จ.สมุทรสงคราม 75000</div>
        <br>
        <div style="text-align: center; font-size: 20px;"><b>ใบสำคัญจ่ายเงิน</b></div>
        <br>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="width: 80%; text-align: right;">วันที่</td>
                <td style="border-bottom: 1px solid black; text-align: center;">${dateStr}</td>
            </tr>
        </table>
        <br>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="width: 20%;">จ่ายให้แก่</td>
                <td style="border-bottom: 1px solid black; width: 30%;">${order?.customer?.name ?? '-'}</td>
                <td style="width: 20%;">เลขบัตรประชาชน</td>
                <td style="border-bottom: 1px solid black; width: 30%;">${order?.customer?.tax ?? '-'}</td>
            </tr>
            <tr>
                <td>ที่อยู่</td>
                <td colspan="3" style="border-bottom: 1px solid black;">${order?.customer?.address ?? '-'}</td>
            </tr>
            <tr>
                <td>ทะเบียน</td>
                <td style="border-bottom: 1px solid black;">${order?.licensePlate ?? '-'}</td>
            </tr>
        </table>
        <br><br>
        <table style="width: 100%; border-collapse: collapse; visibility: visible;">
            <tr>
                <th style="border: 1px solid black;">ลำดับ</th>
                <th style="border: 1px solid black;">รายการ</th>
                <th style="border: 1px solid black;">จำนวน</th>
                <th style="border: 1px solid black;">หน่วยละ</th>
                <th style="border: 1px solid black;">จำนวนเงิน</th>
            </tr>
            ${orderItems}
            <tr>
                <td colspan="2" style="text-align: center; ">(${bahttext(order?.grandTotal)})</td>
                <td colspan="2" style="text-align: center; border: 1px solid black;">รวม</td>
                <td style="text-align: center; border: 1px solid black;">${order?.grandTotal}</td>
            </tr>
        </table>
        
        <br>
        <div>${order?.customerBank?.accountNumber ?? ''} ${order?.customerBank?.accountName ?? ''} ${order?.customerBank?.bank ?? ''}</div>
        <br>
        <div>จ่ายชำระโดย</div>
        <div style="display: flex;align-items: center;gap: 4px;">${checked} ${order?.paymentMethod?.name}</div>
        <br><br>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style=" text-align: center;">............................................</td>
                <td style=" width: 40%;"></td>
                <td style=" text-align: center; ">............................................</td>
            </tr>
            <tr>
                <td style=" text-align: center;">(${order?.customer?.name ?? ''})
                </td>
                <td style=" width: 40%;"></td>
                <td style=" text-align: center;">(............................................)</td>
            </tr>
            <tr>
                <td style=" text-align: center;">ผู้รับเงิน</td>
                <td style=" width: 40%;"></td>
                <td style=" text-align: center;">ผู้จ่ายเงิน</td>
            </tr>
        </table>
        <br>
        ${identityCard}
    </body>
    
    </html>
`

    // const html = readFileSync(content, 'utf-8');

    await page.setContent(content, { waitUntil: 'networkidle2' });

    const pdfBuffer = await page.pdf({ format: 'A4' });
    // const doc = new jsPDF({
    //   orientation: 'p',
    //   unit: 'mm',
    //   format: 'A4'
    // });

    // doc.text("Hello world!", 1, 1);
    // doc.save()
    // return doc.output('blob');
    await browser.close();

    return pdfBuffer
  }
  // async report(concertId: string) {
  //   const carts = await this.cartRepository.find({
  //     select: {
  //       user: {
  //         firstname: true,
  //         lastname: true,
  //         phoneNumber: true,
  //         email: true,
  //       },
  //     },
  //     where: { cartDetails: { concertShow: { concert: { id: concertId, } }, }, },
  //     loadEagerRelations: false,
  //     relations: {
  //       user: true,
  //       cartDetails: {
  //         ticket: true,
  //       },
  //       payments: true,
  //     },
  //     order: {
  //       no: 'ASC',
  //       payments: {
  //         no: 'ASC',
  //       },
  //       cartDetails: {
  //         ticket: {
  //           no: 'ASC',
  //         }
  //       }
  //     }
  //   });

  //   //page1
  //   const header = [[
  //     'หมายเลขคำสั่งซื้อ',
  //     'ยอดรวม',
  //     'สถานะ',
  //     'ชื่อ',
  //     'นามสกุล',
  //     'เบอร์โทร',
  //     'อีเมลล์',
  //     'วันที่ซื้อ',
  //     'เวลาที่ซื้อ',
  //     'หมายเลขบัตร',
  //     'เลขทีนั่ง',
  //     'ราคาบัตร',
  //     'เลขธุรกรรม (2c2p)',
  //     'ราคา (2c2p)',
  //     'สถานะ (2c2p)',
  //   ]]

  //   const data = [];
  //   for (let index = 0; index < carts.length; index++) {
  //     const cart = carts[index];

  //     for (let i = 0; i < cart.payments.length; i++) {
  //       const payment = cart.payments[i];
  //       let row = [];
  //       if (i == 0) {
  //         row = [
  //           cart.no,
  //           cart.cartPrice,
  //           cart.cartStatus,
  //           cart.user.firstname,
  //           cart.user.lastname,
  //           cart.user.phoneNumber,
  //           cart.user.email,
  //           moment(cart.createdAt).format('DD/MM/YYYY'),
  //           new Date(cart.createdAt).toLocaleTimeString('th-TH'),
  //           join(cart.cartDetails.map(c => c.ticket?.ticketNo), ','),
  //           join(cart.cartDetails.map(c => c.ticket?.seatName), ','),
  //           join(cart.cartDetails.map(c => c.ticket?.ticketPrice), ','),
  //           payment.no,
  //           payment.price,
  //           payment.paymentStatus,
  //         ];
  //       } else {
  //         row = [
  //           '',
  //           '',
  //           '',
  //           '',
  //           '',
  //           '',
  //           '',
  //           '',
  //           '',
  //           '',
  //           '',
  //           '',
  //           payment.no,
  //           payment.price,
  //           payment.paymentStatus,
  //         ];
  //       }
  //       data.push(row);
  //     }
  //   }

  //   let buffer = xlsx.build([
  //     // { name: 'Sumary', data: [...header1, [total, person]], options: {} },
  //     { name: 'SheetName', data: [...header, ...data], options: {} }
  //   ]);

  //   return buffer;
  // }


  async exportRewardReport(startDate: Date, endDate: Date, rewardCode: string[]): Promise<Buffer> {
    const query = this.rewardReportRepository.createQueryBuilder('reward_report')
      .innerJoinAndSelect('reward_report.customer', 'customer')
      .innerJoinAndSelect('reward_report.reward', 'reward')
      .where('reward_report.created_at BETWEEN :startDate AND :endDate', { startDate, endDate });
  
    if (rewardCode && rewardCode.length > 0 && rewardCode[0] !== '') {
      query.andWhere('reward.code IN (:...rewardCode)', { rewardCode });
    }
  
    const data = await query
      .select([
        'reward_report.created_at',
        'reward_report.updated_at',
        'customer.code AS mcode',
        'reward_report.customer_name AS customer_name',
        'reward.code AS rcode',
        'reward_report.reward_name AS reward_name',
        'reward_report.amout AS amout',
        'reward_report.point_balance AS point_balance',
      ])
      .getRawMany();
  
  
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Reward Report');
  
    worksheet.columns = [
      { header: 'Created At', key: 'created_at', width: 20 },
      { header: 'Updated At', key: 'updated_at', width: 20 },
      { header: 'Customer Code', key: 'mcode', width: 15 },
      { header: 'Customer Name', key: 'customer_name', width: 25 },
      { header: 'Reward Code', key: 'rcode', width: 15 },
      { header: 'Reward Name', key: 'reward_name', width: 25 },
      { header: 'Amount', key: 'amout', width: 10 },
      { header: 'Point Balance', key: 'point_balance', width: 15 },
    ];
  
    data.forEach((row) => {
      worksheet.addRow({
        created_at: row.created_at,
        updated_at: row.updated_at,
        mcode: row.mcode,
        customer_name: row.customer_name,
        rcode: row.rcode,
        reward_name: row.reward_name,
        amout: row.amout,
        point_balance: row.point_balance,
      });
    });
  
    worksheet.getColumn('created_at').numFmt = 'yyyy-mm-dd hh:mm:ss';
    worksheet.getColumn('updated_at').numFmt = 'yyyy-mm-dd hh:mm:ss';
  
    const uint8Array = await workbook.xlsx.writeBuffer();
    const buffer = Buffer.from(uint8Array);
  
    return buffer;
  }
  
  
  
  
  



  
}
