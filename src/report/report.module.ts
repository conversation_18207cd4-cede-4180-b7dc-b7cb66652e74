import { Module } from '@nestjs/common';
import { ReportService } from './report.service';
import { ReportController } from './report.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OrderModule } from 'src/order/order.module';
import { RewardReport } from 'src/reward/entities/rewardreport.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([RewardReport]),
    OrderModule,
  ],
  controllers: [ReportController],
  providers: [ReportService],
})
export class ReportModule {}
