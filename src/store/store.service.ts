import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { CreateStoreDto } from './dto/create-store.dto';
import { UpdateStoreDto } from './dto/update-store.dto';
import { Store } from './entities/store.entity';
import { Repository, In } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';
import { AssignUsersDto } from './dto/assign-users.dto';
import { User } from '../user/entities/user.entity';

export const STORE_PAGINATION_CONFIG: PaginateConfig<Store> = {
  sortableColumns: ['id', 'name'],
  select: ['id', 'code', 'name', 'createdAt'],
};
@Injectable()
export class StoreService {
  constructor(
    @InjectRepository(Store)
    private storeRepository: Repository<Store>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) { }

  create(createStoreDto: CreateStoreDto) {
    const store = this.storeRepository.create(createStoreDto);

    return this.storeRepository.save(store);
  }

  findAll() {
    return this.storeRepository.find();
  }

  async findOne(id: number) {
    const store = await this.storeRepository.findOne({ where: { id } });

    if (!store) throw new NotFoundException('store not found');

    return store;
  }

  async update(id: number, updateStoreDto: UpdateStoreDto) {
    const store = await this.findOneById(id);
    if (!store) throw new NotFoundException('store not found');

    return this.storeRepository.update(id, updateStoreDto);
  }

  async remove(id: number) {
    const store = await this.findOneById(id);
    if (!store) throw new NotFoundException('store not found');

    await this.storeRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.storeRepository.findOneBy({ id });
  }

  async datatables(query: PaginateQuery): Promise<Paginated<Store>> {
    return paginate(query, this.storeRepository, STORE_PAGINATION_CONFIG);
  }

  async assignUsers(storeId: number, assignUsersDto: AssignUsersDto) {
    const store = await this.storeRepository.findOne({
      where: { id: storeId },
      relations: ['users']
    });

    if (!store) {
      throw new NotFoundException('Store not found');
    }

    // ตรวจสอบว่า users ที่ระบุมีอยู่จริง
    const users = await this.userRepository.find({
      where: { id: In(assignUsersDto.userIds) }
    });

    if (users.length !== assignUsersDto.userIds.length) {
      throw new BadRequestException('Some users not found');
    }

    // กำหนด users ให้ store
    store.users = users;
    await this.storeRepository.save(store);

    return this.findOneWithUsers(storeId);
  }

  async removeUsers(storeId: number, userIds: number[]) {
    const store = await this.storeRepository.findOne({
      where: { id: storeId },
      relations: ['users']
    });

    if (!store) {
      throw new NotFoundException('Store not found');
    }

    // ลบ users ที่ระบุออกจาก store
    store.users = store.users.filter(user => !userIds.includes(user.id));
    await this.storeRepository.save(store);

    return this.findOneWithUsers(storeId);
  }

  async getStoreUsers(storeId: number) {
    const store = await this.storeRepository.findOne({
      where: { id: storeId },
      relations: ['users']
    });

    if (!store) {
      throw new NotFoundException('Store not found');
    }

    return store.users;
  }

  async findOneWithUsers(id: number) {
    const store = await this.storeRepository.findOne({
      where: { id },
      relations: ['users']
    });

    if (!store) {
      throw new NotFoundException('Store not found');
    }

    return store;
  }

  async findAllWithUsers() {
    return this.storeRepository.find({
      relations: ['users'],
      select: {
        id: true,
        code: true,
        name: true,
        address: true,
        createdAt: true,
        users: {
          id: true,
          username: true,
          code: true,
          firstName: true,
          lastName: true,
          phoneNumber: true,
          isActive: true
        }
      }
    });
  }
}
