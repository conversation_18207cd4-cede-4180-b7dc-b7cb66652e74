import { Controller, Get, Post, Body, Put, Param, Delete, ParseIntPipe, HttpCode, HttpStatus } from '@nestjs/common';
import { STORE_PAGINATION_CONFIG, StoreService } from './store.service';
import { CreateStoreDto } from './dto/create-store.dto';
import { UpdateStoreDto } from './dto/update-store.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { AssignUsersDto } from './dto/assign-users.dto';

@Controller('store')
@ApiTags('ร้านค้า')
@Auth()
export class StoreController {
  constructor(private readonly storeService: StoreService) {}

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(STORE_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.storeService.datatables(query);
  }

  @Post()
  create(@Body() createStoreDto: CreateStoreDto) {
    return this.storeService.create(createStoreDto);
  }
  
  @Get()
  findAll() {
    return this.storeService.findAll();
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: string) {
    return this.storeService.findOne(+id);
  }

  @Put(':id')
  update(@Param('id', ParseIntPipe) id: string, @Body() updateStoreDto: UpdateStoreDto) {
    return this.storeService.update(+id, updateStoreDto);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: string) {
    return this.storeService.remove(+id);
  }

  @Get(':id/users')
  @ApiOperation({ summary: 'ดึงรายการ User ที่รับผิดชอบ Store' })
  @ApiParam({ name: 'id', description: 'รหัส Store' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบ Store' })
  getStoreUsers(@Param('id', ParseIntPipe) id: number) {
    return this.storeService.getStoreUsers(id);
  }

  @Post(':id/users')
  @ApiOperation({ summary: 'มอบหมาย User ให้ Store' })
  @ApiParam({ name: 'id', description: 'รหัส Store' })
  @ApiResponse({ status: 200, description: 'มอบหมายสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ข้อมูลไม่ถูกต้อง' })
  @ApiResponse({ status: 404, description: 'ไม่พบ Store หรือ User' })
  assignUsers(@Param('id', ParseIntPipe) id: number, @Body() assignUsersDto: AssignUsersDto) {
    return this.storeService.assignUsers(id, assignUsersDto);
  }

  @Delete(':id/users')
  @ApiOperation({ summary: 'ลบ User ออกจาก Store' })
  @ApiParam({ name: 'id', description: 'รหัส Store' })
  @ApiResponse({ status: 200, description: 'ลบสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบ Store' })
  removeUsers(@Param('id', ParseIntPipe) id: number, @Body() body: { userIds: number[] }) {
    return this.storeService.removeUsers(id, body.userIds);
  }

  @Get(':id/with-users')
  @ApiOperation({ summary: 'ดึงข้อมูล Store พร้อม User ที่รับผิดชอบ' })
  @ApiParam({ name: 'id', description: 'รหัส Store' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบ Store' })
  findOneWithUsers(@Param('id', ParseIntPipe) id: number) {
    return this.storeService.findOneWithUsers(id);
  }

  @Get('all/with-users')
  @ApiOperation({ summary: 'ดึงรายการ Store ทั้งหมดพร้อม User ที่รับผิดชอบ' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  findAllWithUsers() {
    return this.storeService.findAllWithUsers();
  }
}
