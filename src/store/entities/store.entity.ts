import { Column, Entity, Index, OneToMany, ManyToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Branch } from "../../branch/entities/branch.entity";
import { User } from "../../user/entities/user.entity";

@Entity()
@Unique(['code'])
export class Store extends CustomBaseEntity {
    @Column()
    @Index({ unique: true })
    code: string;

    @Column()
    name: string;

    @Column({ nullable: true })
    address: string;

    @OneToMany(() => Branch, (_) => _.store)
    branchs: Array<Branch>;

    @ManyToMany(() => User, (user) => user.stores)
    users: User[];

    constructor(partial?: Partial<Store>) {
        super();
        if (partial) {
            Object.assign(this, partial)
        }
    }
}
