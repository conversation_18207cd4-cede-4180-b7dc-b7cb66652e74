import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateOrderDto, CreateOrderQueueDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { OrderRepository } from './repository/order.repository';
import { Order, OrderStatus } from './entities/order.entity';
import { ProductService } from 'src/product/product.service';
import { OrderItemRepository } from './repository/order-item.repository';
import {
  FilterOperator,
  PaginateConfig,
  PaginateQuery,
  Paginated,
  paginate,
} from 'nestjs-paginate';
import { CreateOrderPaymentDto } from './dto/create-order-payment.dto';
import { OrderPaymentRepository } from './repository/order-payment.repository';
import {
  OrderPayment,
  OrderPaymentStatus,
} from './entities/order-payment.entity';
import {
  PaymentMethod,
  PaymentMethodType,
} from 'src/payment-method/entities/payment-method.entity';
import { Between, Not, Repository } from 'typeorm';
import { PaymentService } from 'src/payment/payment.service';
import { OrderItemAttributeRepository } from './repository/order-item-attribute.repository';
import { OrderItemAttributeValueRepository } from './repository/order-item-attribute-value.repository';
import { Customer } from 'src/customer/entities/customer.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { generateOrderNum } from './order.helper';
import { OrderItem } from './entities/order-item.entity';
import { OrderPhoto } from './entities/order-photo.entity';

export const ORDER_PAGINATION_CONFIG: PaginateConfig<Order> = {
  searchableColumns: ['orderNo', 'branch.name', 'customer.name', 'user.firstName'],
  sortableColumns: ['id', 'orderDate', 'orderStatus', 'branch.name'],
  relations: {
    paymentMethod: true,
    user: true,
    customer: true,
    branch: true
  },
  defaultSortBy: [['orderDate', 'DESC']],
  filterableColumns: {
    orderDate: [FilterOperator.BTW],
    orderStatus: [FilterOperator.EQ],
    'branch.id': [FilterOperator.EQ],
    'user.id': [FilterOperator.EQ],
    'paymentMethod.id': [FilterOperator.EQ],
  },
};

@Injectable()
export class OrderService {
  constructor(
    private orderRepository: OrderRepository,
    private orderItemRepository: OrderItemRepository,
    private orderItemAttributeRepository: OrderItemAttributeRepository,
    private orderItemAttributeValueRepository: OrderItemAttributeValueRepository,
    private orderPaymentRepository: OrderPaymentRepository,
    private productService: ProductService,
    private paymentService: PaymentService,
    @InjectRepository(Customer)
    private customerRepository: Repository<Customer>,
  ) { }

  async create(createOrderDto: CreateOrderDto, userId: number) {
    const discount = 0;

    const order = await this.orderRepository.create(createOrderDto, userId);

    for (const orderItemDto of createOrderDto.orderItems) {
      const orderItem = await this.orderItemRepository.createOrderItem(
        order.id,
        orderItemDto,
      );
    }

    const order_detail = await this.orderRepository.findOne(order.id);
    let point = 0;
    if (!order_detail) {
      throw new NotFoundException(`Order ${order.id} not found`);
    }

    await this.customerRepository.increment({ id: createOrderDto.customerId }, 'point_balance', point);

    return this.orderRepository.findOne(order.id);
  }

  findAll() {
    return this.orderRepository.findAll();
  }

  async findOne(id: number) {
    const order = await Order.findOne({
      where: { id },
      relations: {
        orderItems: {
          product: {
            category: true
          },
          attributes: {
            attributeValues: true,
          }
        },
        orderPayments: {
          paymentMethod: true
        },
        user: true,
        customer: {
          identityCard: true,
          // level: true
        },
        paymentMethod: true,
        customerBank: true,
        branch: true,
        photos: true,
      },
    });
    if (!order) {
      throw new NotFoundException(`Order ${id} not found`);
    }

    return order;
  }

  update(id: number, updateOrderDto: UpdateOrderDto) {
    return `This action updates a #${id} order`;
  }

  remove(id: number) {
    return `This action removes a #${id} order`;
  }

  async datatables(query: PaginateQuery): Promise<Paginated<Order>> {
    return paginate(
      query,
      this.orderRepository.repository,
      ORDER_PAGINATION_CONFIG,
    );
  }

  async createOrderPayment(orderId: number, dto: CreateOrderPaymentDto) {
    const order = await this.orderRepository.findOne(orderId);
    if (!order) {
      throw new NotFoundException(`Order ${orderId} not found`);
    }

    if (order.orderStatus != OrderStatus.SELECT_PAYMENT) {
      throw new BadRequestException('Invalid status');
    }

    for (const orderPaymentDto of dto.orderPayments) {
      const orderPaymentData = new OrderPayment({
        amount: orderPaymentDto.amount,
        paymentMethod: new PaymentMethod({
          id: orderPaymentDto.paymentMethodId,
        }),
        remark: orderPaymentDto.remark,
        order: order,
        status: OrderPaymentStatus.WAIT,
      });

      await this.orderPaymentRepository.createOrderPayment(orderPaymentData);
    }

    await this.orderRepository.repository.save({
      id: orderId,
      orderStatus: OrderStatus.WAIT_PAYMENT,
    });

    return this.orderRepository.findOne(orderId);
  }

  async createOnlineOrderPayment(orderId: number, orderPaymentId: number) {
    const orderPayment = await this.orderPaymentRepository.repository.findOne({
      where: {
        id: orderPaymentId,
        order: { id: orderId },
      },
      relations: {
        paymentMethod: true,
      },
    });

    if (!orderPayment) {
      throw new NotFoundException(`Order payment ${orderPaymentId} not found`);
    }

    return this.paymentService.createPayment(orderPayment);
  }

  async orderAllPaid(id: number) {
    const order = await this.orderRepository.repository.findOneBy({ id });
    if (!order) {
      throw new NotFoundException(`Order ${id} not found`);
    }

    return this.orderRepository.repository.save({
      id: order.id,
      orderStatus: OrderStatus.COMPLETE,
    });
  }

  async orderPaidWithId(orderId: number, id: number) {
    const orderPayment = await this.orderPaymentRepository.repository.findOneBy(
      { id },
    );
    if (!orderPayment) {
      throw new NotFoundException(`Order payment ${id} not found`);
    }

    orderPayment.status = OrderPaymentStatus.SUCCESS;
    orderPayment.save();

    if (await this.isOrderPaidAll(orderId)) {
      await this.orderRepository.repository.update(orderId, {
        orderStatus: OrderStatus.COMPLETE,
      });
    }
  }

  async cancel(orderId: number) {
    const order = await this.orderRepository.repository.findOneBy({
      id: orderId,
    });
    if (!order) {
      throw new NotFoundException(`Order ${orderId} not found`);
    }

    order.orderStatus = OrderStatus.CANCEL;

    return this.orderRepository.repository.save(order);
  }

  async isOrderPaidAll(orderId: number) {
    const orderAndCount =
      await this.orderPaymentRepository.repository.findAndCount({
        where: {
          order: { id: orderId },
          status: Not(OrderPaymentStatus.SUCCESS),
        },
      });

    return orderAndCount[1] == 0;
  }

  async createNextPayment(orderId: number) {
    const order = await this.orderRepository.findOne(orderId);
    if (!order) {
      throw new NotFoundException(`Order ${orderId} not found`);
    }

    const orderPayment = await this.orderPaymentRepository.repository.findOne({
      relations: { paymentMethod: true },
      where: {
        order: { id: orderId },
        status: OrderPaymentStatus.WAIT,
      },
      order: { createdAt: 'ASC' },
    });

    if (!orderPayment) {
      return {
        next: false,
        orderPayment: null,
        payment: null,
      };
    }

    let payment = null;
    if (orderPayment.paymentMethod.type == PaymentMethodType.THAIQR) {
      payment = await this.paymentService.createPayment(orderPayment);
    }

    return {
      next: true,
      orderPayment,
      payment,
    };
  }

  async report(startDate: Date, endDate: Date) {
    return this.orderRepository.repository.find({
      relations: {
        shift: {
          user: true,
          branch: {
            store: true,
          },
        },
      },
      where: {
        createdAt: Between(startDate, endDate),
      },
    });
  }

  async createOrder(createOrderDto: CreateOrderQueueDto, userId: number) {
    const orderNo = await generateOrderNum(createOrderDto.branchId);

    const order = Order.create({
      orderNo: orderNo,
      orderDate: new Date(),
      total: createOrderDto.total,
      discount: 0,
      orderStatus: OrderStatus.SELECT_PAYMENT,
      grandTotal: createOrderDto.grandTotal,
      user: {
        id: userId ?? null,
      },
      licensePlate: createOrderDto?.licensePlate,
      paymentMethod: {
        id: createOrderDto?.paymentMethodId,
      },
      customer: {
        id: createOrderDto?.customerId,
      },
      branch: {
        id: createOrderDto?.branchId,
      },
      queueIds: createOrderDto.queueIds,
      contact: createOrderDto?.contact,
    });

    await order.save();

    const orderItems = [];
    for (const orderItemDto of createOrderDto.orderItems) {
      const orderItem = OrderItem.create({
        ...orderItemDto,
        quantity: orderItemDto.weight,
        total: orderItemDto.total,
        order: {
          id: order.id ?? null,
        },
        product: {
          id: orderItemDto?.productId ?? null,
        },
      });
      orderItems.push(orderItem);
    }
    await OrderItem.save(orderItems);

    if (createOrderDto.orderPhotos && createOrderDto.orderPhotos.length > 0) {
      const orderPhotos: OrderPhoto[] = [];

      for (const photoDto of createOrderDto.orderPhotos) {
        const orderPhoto = OrderPhoto.create({
          path: photoDto.path,
          order: { id: order.id },
        });
        orderPhotos.push(orderPhoto);
      }

      await OrderPhoto.save(orderPhotos);
    }

    return this.orderRepository.findOne(order.id);
  }
}
