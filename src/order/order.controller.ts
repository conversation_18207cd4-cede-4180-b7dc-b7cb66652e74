import { Controller, Get, Post, Body, Put, Param, Delete, UseInterceptors, HttpCode, HttpStatus, ParseIntPipe, Req } from '@nestjs/common';
import { ORDER_PAGINATION_CONFIG, OrderService } from './order.service';
import { CreateOrderDto, CreateOrderQueueDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { TransactionInterceptor } from 'src/common/interceptor/transaction.interceptor';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { CreateOrderPaymentDto } from './dto/create-order-payment.dto';
import { ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';

@Controller('order')
@ApiTags('สร้างออเดอร์')
@Auth()
export class OrderController {
  constructor(private readonly orderService: OrderService) { }

  // @Post('test')
  // test() {
  //   return this.orderService.test()
  // }

  @Post(':id/paid/:orderPaymentId')
  @ApiOperation({ summary: 'ชำระเงินสำเร็จ' })
  @UseInterceptors(TransactionInterceptor)
  async orderAllPaid(
    @Param('id', ParseIntPipe) orderId: string,
    @Param('orderPaymentId', ParseIntPipe) orderPaymentId: string
  ) {
    await this.orderService.orderPaidWithId(+orderId, +orderPaymentId);

    return this.orderService.findOne(+orderId);
  }

  @Post(':id/cancel')
  @ApiOperation({ summary: 'ยกเลิก' })
  @UseInterceptors(TransactionInterceptor)
  cancel(@Param('id', ParseIntPipe) orderId: string) {
    return this.orderService.cancel(+orderId);
  }

  @Post(':id/next-payment')
  @ApiOperation({ summary: 'ดึงข้อมูลชำระเงิน' })
  @UseInterceptors(TransactionInterceptor)
  createNextOrderPayment(@Param('id', ParseIntPipe) orderId: string) {

    return this.orderService.createNextPayment(+orderId);
  }

  @Post(':id/payment/:orderPaymentId')
  @ApiOperation({ summary: 'ชำระเงินออนไลน์' })
  @UseInterceptors(TransactionInterceptor)
  createOnlineOrderPayment(
    @Param('id', ParseIntPipe) orderId: string,
    @Param('orderPaymentId', ParseIntPipe) orderPaymentId: string) {

    return this.orderService.createOnlineOrderPayment(+orderId, +orderPaymentId);
  }

  @Post(':id/payment')
  @ApiOperation({ summary: 'เลือกการชำเงิน' })
  @UseInterceptors(TransactionInterceptor)
  createOrderPayment(@Param('id', ParseIntPipe) orderId: string, @Body() dto: CreateOrderPaymentDto) {
    return this.orderService.createOrderPayment(+orderId, dto);
  }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(ORDER_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.orderService.datatables(query);
  }

  @Post('/create-order')
  @ApiOperation({ summary: 'สร้าง Order จากคิว' })
  createOrder(@Req() req, @Body() createOrderDto: CreateOrderQueueDto) {
    const userId = req.user['sub'];

    return this.orderService.createOrder(createOrderDto, userId);
  }

  @Post()
  @UseInterceptors(TransactionInterceptor)
  create(@Req() req, @Body() createOrderDto: CreateOrderDto) {
    const userId = req.user['sub'];

    return this.orderService.create(createOrderDto, userId);
  }

  @Get()
  findAll() {
    return this.orderService.findAll();
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: string) {
    return this.orderService.findOne(+id);
  }

  // @Put(':id')
  // @UseInterceptors(TransactionInterceptor)
  // update(@Param('id') id: string, @Body() updateOrderDto: UpdateOrderDto) {
  //   return this.orderService.update(+id, updateOrderDto);
  // }

  // @Delete(':id')
  // @UseInterceptors(TransactionInterceptor)
  // remove(@Param('id') id: string) {
  //   return this.orderService.remove(+id);
  // }
}
