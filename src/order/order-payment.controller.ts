import { Body, Controller, Param, Post, Put } from '@nestjs/common';
import { OrderPaymentService } from './order-payment.service';
import { Auth } from 'src/auth/decorators/auth.decorator';

@Controller('order-payment')
@Auth()
export class OrderPaymentController {
  constructor(
    private orderPaymentService: OrderPaymentService,
  ) { }

  @Post(':id/paid')
  paid(@Param('id') id: string, @Body() dto) { 
    return this.orderPaymentService.paid(+id)
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() dto) {
    // return this.orderService.update(+id, updateOrderDto);
  }
}
