import { Inject, Injectable, Scope } from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { Request } from "express";
import { BaseRepository } from "src/common/repository/base-repository";
import { Between, DataSource } from "typeorm";
import { Order, OrderStatus } from "../entities/order.entity";
import { CreateOrderDto } from "../dto/create-order.dto";
import { Helper } from "src/common/helper";
import { DateTime } from "luxon";

@Injectable({ scope: Scope.REQUEST })
export class OrderRepository extends BaseRepository {
  constructor(dataSource: DataSource, @Inject(REQUEST) req: Request) {
    super(dataSource, req);
  }

  public get repository() {
    return this.getRepository(Order);
  }

  findAll() {
    return this.getRepository(Order).find()
  }

  findOne(id: number) {
    return this.getRepository(Order).findOne({
      where: { id: id },
      relations: {
        orderItems: {
          product: {
            category: true
          },
          attributes: {
            attributeValues: true,
          }
        },
        orderPayments: {
          paymentMethod: true
        },
        user: true,
        customer: {
          identityCard: true,
          // level: true
        },
        paymentMethod: true,
        customerBank: true,
        branch: true,
      }
    })
  }

  async create(data: CreateOrderDto, userId: number) {
    const orderRepository = this.getRepository(Order);

    const order = orderRepository.create({
      orderNo: await this.generateOrderNum(data.branchId),
      orderDate: new Date(),
      total: data.total,
      discount: 0,
      orderStatus: OrderStatus.SELECT_PAYMENT,
      grandTotal: data.total,
      user: {
        id: userId ?? null,
      },
      licensePlate: data?.licensePlate,
      paymentMethod: {
        id: data?.paymentMethodId,
      },
      customer: {
        id: data?.customerId,
      },
      bankName: data?.bankName,
      accountName: data?.accountName,
      accountNumber: data?.accountNumber,
      branch: {
        id: data.branchId,
      }
    });

    await orderRepository.insert(order);

    return order;
  }

  async generateOrderNum(branchId: number): Promise<number> {
    const orderRepository = this.getRepository(Order);

    const now = DateTime.now().setLocale('th-TH')
    const start = now.set({ hour: 0, minute: 0, second: 0 }).toJSDate();
    const end = now.set({ hour: 23, minute: 59, second: 59 }).toJSDate();

    const lastOrder = await orderRepository.findAndCount({
      where: {
        orderDate: Between(start, end),
        branch: {
          id: branchId
        }
      },
      order: {
        orderDate: "DESC",
      },
    });

    let newOrderNo: string;
    let runOrder = '001'
    const thaidate = Helper.DateToThaiDate(now.toLocaleString({ year: '2-digit', month: '2-digit', day: '2-digit' }))

    if (!lastOrder.length) {
      newOrderNo = thaidate + runOrder
    } else {
      const newNumber = lastOrder[1] + 1
      runOrder = ('' + newNumber).padStart(3, '0')
    }

    newOrderNo = thaidate + runOrder

    return +newOrderNo
  }
}