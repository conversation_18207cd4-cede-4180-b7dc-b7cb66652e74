import { Inject, Injectable, Scope } from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { Request } from "express";
import { BaseRepository } from "src/common/repository/base-repository";
import { DataSource } from "typeorm";
import { CreateOrderItemAttributeDto, CreateOrderItemAttributeValueDto } from "../dto/create-order.dto";
import { OrderItemAtribute } from "../entities/order-item-attribute.entity";
import { OrderItemAtributeValue } from "../entities/order-item-attribute-value.entity";

@Injectable({ scope: Scope.REQUEST })
export class OrderItemAttributeValueRepository extends BaseRepository {
  constructor(dataSource: DataSource, @Inject(REQUEST) req: Request) {
    super(dataSource, req);
  }

  async create(orderItemAttributeId: number, dto: CreateOrderItemAttributeValueDto) {
    const repository = this.getRepository(OrderItemAtributeValue);

    const data = repository.create({
      ...dto,
      orderItemAttribute: {
        id: orderItemAttributeId
      }
    });

    await repository.insert(data);

    return data
  }
}