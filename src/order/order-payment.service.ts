import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { OrderPaymentRepository } from './repository/order-payment.repository';
import { OrderPaymentStatus } from './entities/order-payment.entity';

@Injectable()
export class OrderPaymentService {
    constructor(
        private orderPaymentRepository: OrderPaymentRepository,
    ) { }

    async paid(id: number) {
        const orderPayment = await this.orderPaymentRepository.repository.findOne({
            where: { id: id }
        });

        if (!orderPayment) {
            throw new NotFoundException(`OrderPayment ${id} not found`)
        }

        if (orderPayment.status == OrderPaymentStatus.SUCCESS) {
            throw new BadRequestException(`OrderPayment ${id} invalid status`)
        }

        await this.orderPaymentRepository.paidSuccess(id);

        return orderPayment;
    }
}
