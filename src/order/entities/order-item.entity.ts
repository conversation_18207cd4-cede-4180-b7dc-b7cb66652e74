import { CustomBaseEntity } from "../../common/entities";
import { Product } from "../../product/entities/product.entity";
import { Column, Entity, JoinColumn, ManyToOne, OneToMany } from "typeorm";
import { Order } from "./order.entity";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { OrderItemAtribute } from "./order-item-attribute.entity";

@Entity()
export class OrderItem extends CustomBaseEntity {
  @Column({ type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer() })
  quantity: number

  @Column({ type: 'numeric', default: 0, nullable: true, transformer: new DecimalColumnTransformer() })
  dequantity: number

  @Column({ type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer() })
  price: number;

  @Column({ type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer() })
  total: number

  @Column({ nullable: true })
  uptext: string;

  @Column({ nullable: true })
  downtext: string;

  @Column({ default: false, nullable: true })
  lowGrade: boolean;

  @OneToMany(() => OrderItemAtribute, (_) => _.orderItem)
  attributes: OrderItemAtribute[];

  @ManyToOne(() => Product, (p: Product) => p.orderItems, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @ManyToOne(() => Order, (o: Order) => o.orderItems, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'order_id' })
  order: Order;

  constructor(partial?: Partial<OrderItem>) {
    super();
    if (partial) {
      Object.assign(this, partial)
    }
  }
}
