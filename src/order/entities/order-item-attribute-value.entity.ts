import { CustomBaseEntity } from "../../common/entities";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToO<PERSON> } from "typeorm";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { OrderItemAtribute } from "./order-item-attribute.entity";

@Entity()
export class OrderItemAtributeValue extends CustomBaseEntity {
  @Column({ type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer() })
  price: number;

  @Column({ type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer() })
  total: number;

  @Column()
  quantity: number

  @ManyToOne(() => OrderItemAtribute, (_) => _.attributeValues)
  @JoinColumn({ name: 'order_item_attribute_id' })
  orderItemAttribute: OrderItemAtribute;

  constructor(partial?: Partial<OrderItemAtributeValue>) {
    super();
    if (partial) {
      Object.assign(this, partial)
    }
  }
}
