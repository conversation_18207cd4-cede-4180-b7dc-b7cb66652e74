import { CustomBaseEntity } from "../../common/entities";
import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne } from "typeorm";
import { Order } from "./order.entity";
import { PaymentMethod } from "../../payment-method/entities/payment-method.entity";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";

export enum OrderPaymentStatus {
    WAIT = 'wait',
    SUCCESS = 'success',
}

@Entity()
export class OrderPayment extends CustomBaseEntity {
    @ManyToOne(() => PaymentMethod, (pm) => pm.orderPayments)
    @JoinColumn({ name: 'payment_method_id' })
    paymentMethod: PaymentMethod;

    @Column({ type: 'decimal', precision: 10, scale: 2, transformer: new DecimalColumnTransformer(), })
    amount: number

    @Column({ nullable: true })
    remark: string

    @Column({ type: 'enum', enum: OrderPaymentStatus })
    status: OrderPaymentStatus;

    @ManyToOne(() => Order, (o: Order) => o.orderPayments)
    @JoinColumn({ name: 'order_id' })
    order: Order;

    constructor(partial?: Partial<OrderPayment>) {
        super();
        if (partial) {
            Object.assign(this, partial)
        }
    }
}
