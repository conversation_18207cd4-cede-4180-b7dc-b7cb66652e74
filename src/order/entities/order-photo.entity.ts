import { Column, Entity, ManyToOne } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Order } from "./order.entity";
import { Expose } from "class-transformer";

@Entity()
export class OrderPhoto extends CustomBaseEntity {
    @Column()
    path: string;

    @ManyToOne(() => Order, (order) => order.photos)
    order: Order;

    @Expose()
    get pathUrl(): string {
        if (!this.path) return null;

        // ถ้า path ขึ้นต้นด้วย http:// หรือ https:// ให้ return path เลย
        if (this.path.startsWith('http://') || this.path.startsWith('https://')) {
            return this.path;
        }

        // ถ้าไม่ใช่ URL สมบูรณ์ ให้ต่อ base URL เข้าไป
        return `${process.env.APP_URL}/${this.path}`;
    }
}