import { Column, <PERSON><PERSON><PERSON>, Index, <PERSON>in<PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { OrderPayment } from "./order-payment.entity";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { Shift } from "../../shift/entities/shift.entity";
import { OrderItem } from "./order-item.entity";
import { User } from "../../user/entities/user.entity";
import { Customer } from "../../customer/entities/customer.entity";
import { LicensePlate } from "../../customer/entities/license-plate.entity";
import { PaymentMethod } from "../../payment-method/entities/payment-method.entity";
import { CustomerBank } from "../../customer/entities/customer-bank.entity";
import { Branch } from "../../branch/entities/branch.entity";
import { OrderPhoto } from "./order-photo.entity";

export enum Gender {
    M = "male",
    F = "female",
    O = "other"
}

export enum OrderStatus {
    SELECT_PAYMENT = "select_payment",
    WAIT_PAYMENT = "wait_payment",
    COMPLETE = "complete",
    INCOMPLETE = "incomplete",
    VOID = "void",
    CANCEL = "cancel",
}

@Entity()
export class Order extends CustomBaseEntity {
    @Index()
    @Column({ name: 'order_no' })
    orderNo: number

    @Column({ name: 'order_date' })
    orderDate: Date;

    @Column({ name: 'order_status', type: 'enum', enum: OrderStatus })
    orderStatus: OrderStatus;

    @Column({ name: 'bank_name', nullable: true })
    bankName: string

    @Column({ name: 'account_name', nullable: true })
    accountName: string

    @Column({ name: 'account_number', nullable: true })
    accountNumber: string

    // @Column({ nullable: true })
    // nationality: string;

    // @Column({ type: 'enum', enum: Gender, nullable: true })
    // gender: Gender;

    @Column({ type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer() })
    total: number;

    @Column({ type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer() })
    discount: number;

    @Column({ name: 'grand_total', type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer() })
    grandTotal: number;

    @Column({ name: 'license_plate', nullable: true })
    licensePlate: string;

    @ManyToOne(() => Shift, (s: Shift) => s.orders)
    @JoinColumn({ name: 'shift_id' })
    shift: Shift;

    // @ManyToOne(() => User, (u: User) => u.orders)
    // user: User;

    @OneToMany(() => OrderItem, (ot: OrderItem) => ot.order)
    orderItems: OrderItem[];

    @OneToMany(() => OrderPayment, (op: OrderPayment) => op.order)
    orderPayments: OrderPayment[];

    @ManyToOne(() => User, (_) => _.orders)
    @JoinColumn({ name: 'user_id' })
    user: User;

    @ManyToOne(() => Customer, (_) => _.orders)
    @JoinColumn({ name: 'customer_id' })
    customer: Customer;

    @ManyToOne(() => CustomerBank, (_) => _.orders)
    @JoinColumn({ name: 'customer_bank_id' })
    customerBank: CustomerBank;

    @ManyToOne(() => PaymentMethod, (_) => _.orders)
    @JoinColumn({ name: 'payment_method_id' })
    paymentMethod: PaymentMethod;

    @ManyToOne(() => Branch, (_) => _.orders)
    @JoinColumn({ name: 'branch_id' })
    branch: Branch;

    @Column('simple-array', { nullable: true })
    queueIds: number[];

    @OneToMany(() => OrderPhoto, (_) => _.order)
    photos: OrderPhoto[];

    @Column({ nullable: true })
    contact: string;

    constructor(partial?: Partial<Order>) {
        super();
        if (partial) {
            Object.assign(this, partial)
        }
    }
}
