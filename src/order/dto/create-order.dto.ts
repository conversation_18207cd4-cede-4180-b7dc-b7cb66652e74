import { IsNotEmpty, IsNumber } from 'class-validator';

export class CreateOrderDto {
    // readonly shiftId: number;

    @IsNotEmpty()
    readonly total: number;

    readonly bankName?: string;

    readonly accountName?: string;

    readonly accountNumber?: string;

    readonly customerId?: number;

    @IsNotEmpty()
    readonly branchId: number;

    readonly licensePlate?: string;

    readonly paymentMethodId?: number;

    readonly orderItems: CreateOrderItemDto[];

    readonly point_balance?: number;
}

export class CreateOrderItemDto {
    readonly productId: number;
    readonly price: number;
    readonly quantity: number;
    readonly dequantity: number;
    readonly total: number;
    readonly uptext: string;
    readonly downtext: string;
    // readonly attributes: CreateOrderItemAttributeDto[];
}

export class CreateOrderItemAttributeDto {
    readonly attributeName: string;
    readonly total: number;
    readonly attributeValues: CreateOrderItemAttributeValueDto[];
}

export class CreateOrderItemAttributeValueDto {
    readonly attributeValueName: string;
    readonly quantity: number;
    readonly price: number;
    readonly total: number;
}

export class CreateOrderQueueDto {
    @IsNumber({}, { each: true })
    readonly queueIds: number[];

    @IsNumber()
    readonly branchId: number;

    @IsNumber()
    readonly paymentMethodId: number;

    @IsNumber()
    readonly customerId: number;

    readonly licensePlate?: string;

    readonly total: number;

    readonly grandTotal: number;

    readonly contact?: string;

    readonly orderItems: CreateOrderQueueItemDto[];

    readonly orderPhotos?: OrderPhotoDto[];
}

export class CreateOrderQueueItemDto {

    readonly productId: number;

    readonly unitId: number;

    readonly weight: number;

    readonly price: number;

    readonly total: number;
}

export class OrderPhotoDto {
    readonly path: string;
}