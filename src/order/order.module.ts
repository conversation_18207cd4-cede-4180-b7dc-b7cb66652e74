import { Module } from '@nestjs/common';
import { OrderService } from './order.service';
import { OrderController } from './order.controller';
import { OrderRepository } from './repository/order.repository';
import { ProductModule } from 'src/product/product.module';
import { OrderItemRepository } from './repository/order-item.repository';
import { OrderPaymentRepository } from './repository/order-payment.repository';
import { OrderPaymentController } from './order-payment.controller';
import { OrderPaymentService } from './order-payment.service';
import { PaysolutionModule } from 'src/paysolution/paysolution.module';
import { PaymentModule } from 'src/payment/payment.module';
import { OrderItemAttributeRepository } from './repository/order-item-attribute.repository';
import { OrderItemAttributeValueRepository } from './repository/order-item-attribute-value.repository';
import { CustomerModule } from 'src/customer/customer.module';
import { CustomerService } from 'src/customer/customer.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CustomerBank } from 'src/customer/entities/customer-bank.entity';
import { LicensePlate } from 'src/customer/entities/license-plate.entity';
import { Customer } from 'src/customer/entities/customer.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Customer, LicensePlate, CustomerBank]),
    ProductModule, PaymentModule,CustomerModule],
  controllers: [OrderController],
  providers: [OrderService, OrderRepository, OrderItemRepository, OrderPaymentRepository,
    OrderPaymentService, OrderItemAttributeRepository, OrderItemAttributeValueRepository,CustomerService],
  exports: [OrderService],
})
export class OrderModule { }
