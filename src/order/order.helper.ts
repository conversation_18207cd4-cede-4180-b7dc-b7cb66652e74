import { DateTime } from "luxon";
import { Helper } from "src/common/helper";
import { Between } from "typeorm";
import { Order } from "./entities/order.entity";

export async function generateOrderNum(branchId: number): Promise<number> {

  const now = DateTime.now().setLocale('th-TH')
  const start = now.set({ hour: 0, minute: 0, second: 0 }).toJSDate();
  const end = now.set({ hour: 23, minute: 59, second: 59 }).toJSDate();

  const lastOrder = await Order.findAndCount({
    where: {
      orderDate: Between(start, end),
      branch: {
        id: branchId
      }
    },
    order: {
      orderDate: "DESC",
    },
  });

  let newOrderNo: string;
  let runOrder = '001'
  const thaidate = Helper.DateToThaiDate(now.toLocaleString({ year: '2-digit', month: '2-digit', day: '2-digit' }))

  if (!lastOrder.length) {
    newOrderNo = thaidate + runOrder
  } else {
    const newNumber = lastOrder[1] + 1
    runOrder = ('' + newNumber).padStart(3, '0')
  }

  newOrderNo = thaidate + runOrder

  return +newOrderNo
}