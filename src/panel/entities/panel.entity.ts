import { Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>, <PERSON>ToMany } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Product } from "../../product/entities/product.entity";

@Entity()
export class Panel extends CustomBaseEntity {
  @Column()
  name: string;

  @ManyToMany(() => Product)
  @JoinTable({
    inverseJoinColumn: { name: 'panel_id' },
    joinColumn: { name: 'product_id' }
  })
  products: Product[]
}
