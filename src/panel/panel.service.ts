import { Injectable, NotFoundException } from '@nestjs/common';
import { CreatePanelDto } from './dto/create-panel.dto';
import { UpdatePanelDto } from './dto/update-panel.dto';
import { Panel } from './entities/panel.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { FindOptionsWhere, Repository } from 'typeorm';
import { Product } from 'src/product/entities/product.entity';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';

export const PANEL_PAGINATION_CONFIG: PaginateConfig<Panel> = {
  sortableColumns: ['name'],
  searchableColumns: ['name'],
  relativePath: true,
};

@Injectable()
export class PanelService {
  constructor(
    @InjectRepository(Panel)
    private panelRepository: Repository<Panel>,
  ) { }

  async create(createPanelDto: CreatePanelDto) {
    const panel = this.panelRepository.create(createPanelDto)

    const products = createPanelDto.productIds.map(e => {
      return { id: e } as Product;
    });

    panel.products = products;

    return this.panelRepository.save(panel);
  }

  findAll() {
    return this.panelRepository.find({
      relations: {
        products: {
          category: true,
          image: true,
        },
      }
    });
  }

  async findOne(id: number, levelId: number) {

    const panel = await this.panelRepository.findOne({
      where: {
        id: id,
        ...(levelId && {
          products: {
            productLevels: {
              level: {
                id: levelId
              }
            }
          }
        })
      },
      relations: {
        products: {
          category: true,
          image: true,
          productLevels: {
            level: true
          }
        }
      }
    });

    if (!panel) {
      throw new NotFoundException("panel not found")
    }

    for (let index = 0; index < panel.products.length; index++) {
      const product = panel.products[index];
      
      if (product.productLevels.length) {
        product.price = product.productLevels[0].price;
      }
    }

    return panel;
  }

  async update(id: number, updatePanelDto: UpdatePanelDto) {
    const panel = await this.panelRepository.findOne({
      where: { id },
      relations: { products: true }
    })

    if (!panel) {
      throw new NotFoundException("panel not found")
    }

    // panel.products = []

    // await this.panelRepository.save(panel)

    const products = updatePanelDto.productIds.map(e => {
      return { id: e } as Product;
    });

    panel.products = products;

    return this.panelRepository.save(panel);
  }

  async remove(id: number) {
    const panel = await this.panelRepository.findOneBy({ id })

    if (!panel) {
      throw new NotFoundException("panel not found")
    }

    await this.panelRepository.softRemove(panel);
  }

  async datatables(query: PaginateQuery): Promise<Paginated<Panel>> {
    return paginate(query, this.panelRepository, PANEL_PAGINATION_CONFIG);
  }
}
