import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  ParseIntPipe,
  Req,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ROLE_PAGINATION_CONFIG, RoleService } from './role.service';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { Request } from 'express';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';

@Controller('role')
@ApiTags('สิทธิ์')
@Auth()
export class RoleController {
  constructor(private readonly roleService: RoleService) { }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(ROLE_PAGINATION_CONFIG)
  datatables(@Req() req: Request, @Paginate() query: PaginateQuery) {
    const user = req.user;

    return this.roleService.datatables(query, user);
  }

  @Post()
  create(@Req() req: Request, @Body() createRoleDto: CreateRoleDto) {
    const user = req.user;

    return this.roleService.create(createRoleDto, user);
  }

  @Get()
  findAll() {
    return this.roleService.findAll();
  }

  @Get('my-roles')
  @ApiOperation({ summary: 'ดู roles ทั้งหมดในร้านที่ user คนนั้นอยู่' })
  findAllMyRoles(@Req() req: Request) {
    const currentuserId = req.user['sub'];
    return this.roleService.findAllMyRoles(currentuserId);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: string) {
    return this.roleService.findOne(+id);
  }

  @Put(':id')
  update(@Param('id', ParseIntPipe) id: string, @Body() updateRoleDto: UpdateRoleDto) {
    return this.roleService.update(+id, updateRoleDto);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: string) {
    return this.roleService.remove(+id);
  }
}
