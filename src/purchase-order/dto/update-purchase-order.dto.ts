import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreatePurchaseOrderDto } from './create-purchase-order.dto';
import { IsDateString, IsEnum, IsOptional } from 'class-validator';
import { PurchaseOrderStatus } from '../entities/purchase-order.entity';

export class UpdatePurchaseOrderDto extends PartialType(CreatePurchaseOrderDto) {
    @ApiProperty({ description: 'วันที่ได้รับสินค้าจริง', required: false })
    @IsOptional()
    @IsDateString()
    actualDeliveryDate?: string;

    @ApiProperty({ description: 'รหัสผู้อนุมัติ', required: false })
    @IsOptional()
    approvedBy?: number;

    @ApiProperty({ description: 'วันที่อนุมัติ', required: false })
    @IsOptional()
    @IsDateString()
    approvedAt?: string;
}
