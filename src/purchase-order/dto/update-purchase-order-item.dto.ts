import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreatePurchaseOrderItemDto } from './create-purchase-order-item.dto';
import { IsNumber, IsOptional, Min } from 'class-validator';

export class UpdatePurchaseOrderItemDto extends PartialType(CreatePurchaseOrderItemDto) {
    @ApiProperty({ description: 'จำนวนที่ได้รับ', required: false })
    @IsOptional()
    @IsNumber()
    @Min(0, { message: 'จำนวนที่ได้รับต้องมากกว่าหรือเท่ากับ 0' })
    receivedQuantity?: number;

    @ApiProperty({ description: 'จำนวนที่เหลือ', required: false })
    @IsOptional()
    @IsNumber()
    @Min(0, { message: 'จำนวนที่เหลือต้องมากกว่าหรือเท่ากับ 0' })
    remainingQuantity?: number;
}
