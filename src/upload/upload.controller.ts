import { ClassSerializerInterceptor, Controller, Delete, Param, Post, UploadedFile, UploadedFiles, UseInterceptors } from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { UploadService } from './upload.service';
import { ApiBody, ApiConsumes, ApiResponse } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';

@Controller('upload')
@Auth()
// @UseInterceptors(ClassSerializerInterceptor)
export class UploadController {
  constructor(
    private uploadService: UploadService,
  ) { }

  @Post('file')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary'
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'File uploaded successfully',
    schema: {
      type: 'object',
      properties: {
        fieldname: { type: 'string', example: 'file' },
        originalname: {
          type: 'string',
          example: 'E334570B-550F-4C2E-BAAB-C97A4B064CF7_1_102_a.jpeg',
        },
        encoding: { type: 'string', example: '7bit' },
        mimetype: { type: 'string', example: 'image/jpeg' },
        destination: { type: 'string', example: './uploads' },
        filename: {
          type: 'string',
          example: '2025/06/18/NVGU6STdRDgc0kpcwytC0ZK5izGICKMq.jpeg',
        },
        path: {
          type: 'string',
          example: 'uploads/2025/06/18/NVGU6STdRDgc0kpcwytC0ZK5izGICKMq.jpeg',
        },
        size: { type: 'number', example: 965138 },
        provider: { type: 'string', example: 'local' },
        pathUrl: {
          type: 'string',
          example:
            'http://localhost:3000/2025/06/18/NVGU6STdRDgc0kpcwytC0ZK5izGICKMq.jpeg',
        },
      },
    },
  })
  async uploadFile(@UploadedFile() file: Express.Multer.File) {
    await this.uploadService.createOne(file);

    const _file: any = file;
    _file.pathUrl = _file.provider == 'local'
      ? process.env.APP_URL + '/' + _file.filename
      : _file.filename;

    return _file;
  }

  @Post('files')
  @UseInterceptors(FilesInterceptor('files'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          }
        },
      },
    },
  })
  async uploadFiles(@UploadedFiles() files: Array<Express.Multer.File>) {
    await this.uploadService.createMany(files);

    for (const file of files as any) {
      file.pathUrl = file.provider == 'local'
        ? process.env.APP_URL + '/' + file.filename
        : file.filename;
    }

    return files;
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.uploadService.remove(+id);
  }
}
