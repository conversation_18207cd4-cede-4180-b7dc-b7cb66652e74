import { Product } from "../../product/entities/product.entity";
import { CustomBaseEntity } from "../../common/entities";
import { Column, Entity, Index, OneToMany } from "typeorm";

@Entity()
export class Category extends CustomBaseEntity {
    @Column()
    @Index({ unique: true })
    code: string;

    @Column()
    name: string;

    @Column({ name: 'color_code', nullable: true })
    colorCode: string;

    @OneToMany(() => Product, (_) => _.category)
    products: Array<Product>;

    constructor(partial?: Partial<Category>) {
        super();
        if (partial) {
            Object.assign(this, partial)
        }
    }
}
