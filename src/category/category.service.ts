import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { Category } from './entities/category.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';

export const CATEGORY_PAGINATION_CONFIG: PaginateConfig<Category> = {
    sortableColumns: ['id', 'name'],
    select: ['id', 'code', 'name', 'colorCode', 'createdAt'],
};
@Injectable()
export class CategoryService {
    constructor(
        @InjectRepository(Category)
        private categoryRepository: Repository<Category>,
    ) { }

    create(createCategoryDto: CreateCategoryDto) {
        const category = this.categoryRepository.create(createCategoryDto);

        return this.categoryRepository.save(category);
    }

    findAll() {
        return this.categoryRepository.find({
            order: {
                code: "ASC",
            },
        });
    }

    async findOne(id: number) {
        const category = await this.categoryRepository.findOne({
            where: { id, }
        });
        if (!category) throw new NotFoundException("category not found");

        return category;
    }

    async update(id: number, updateCategoryDto: UpdateCategoryDto) {
        const category = await this.findById(id);

        if (!category) throw new NotFoundException("category not found");

        return this.categoryRepository.update(id, updateCategoryDto);
    }

    async remove(id: number) {
        const category = await this.findById(id);

        if (!category) throw new NotFoundException("category not found");

        category.code = new Date() + category.code
        await category.save()

        await this.categoryRepository.softRemove(category);
    }

    findById(id: number) {
        return this.categoryRepository.findOneBy({ id });
    }

    async datatables(query: PaginateQuery): Promise<Paginated<Category>> {
        return paginate(query, this.categoryRepository, CATEGORY_PAGINATION_CONFIG);
    }
}
