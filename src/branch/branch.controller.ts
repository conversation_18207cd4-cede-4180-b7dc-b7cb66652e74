import { Controller, Get, Post, Body, Put, Param, Delete, ParseIntPipe, HttpCode, HttpStatus } from '@nestjs/common';
import { BRANCH_PAGINATION_CONFIG, BranchService } from './branch.service';
import { CreateBranchDto } from './dto/create-branch.dto';
import { UpdateBranchDto } from './dto/update-branch.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApiTags } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';

@Controller('branch')
@ApiTags('สาขา')
export class BranchController {
  constructor(private readonly branchService: BranchService) {}

  @Auth()
  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(BRANCH_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.branchService.datatables(query);
  }
  
  @Auth()
  @Post()
  create(@Body() createBranchDto: CreateBranchDto) {
    return this.branchService.create(createBranchDto);
  }
  
  @Get()
  findAll() {
    return this.branchService.findAll();
  }
  
  @Auth()
  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: string) {
    return this.branchService.findOne(+id);
  }
  
  @Auth()
  @Put(':id')
  update(@Param('id', ParseIntPipe) id: string, @Body() updateBranchDto: UpdateBranchDto) {
    return this.branchService.update(+id, updateBranchDto);
  }
  
  @Auth()
  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: string) {
    return this.branchService.remove(+id);
  }
}
