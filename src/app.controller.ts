import { Controller, Get, ParseIntPipe, Query } from '@nestjs/common';
import { AppService } from './app.service';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) { }

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('/dashboard')
  getDashboard() {
    return this.appService.dashboard();
  }

  @Get('/top-10-product')
  getTop10Product() {
    return this.appService.top10Product();
  }

  @Get('/last-10-order')
  getLast10Orders() {
    return this.appService.last10Orders();
  }

  @Get('/top-seller')
  getTopSeller(
    @Query('year', ParseIntPipe) year: number,
    @Query('month', ParseIntPipe) month: number,
  ) {
    return this.appService.topSellers(year, month);
  }

  @Get('/top-seller-details')
  getTopSellerDetail(
    @Query('year', ParseIntPipe) year: number,
    @Query('month', ParseIntPipe) month: number,
    @Query('userId', ParseIntPipe) userId: number,
  ) {
    return this.appService.topSellerDetail(userId, year, month);
  }

  @Get('/top-customer')
  getTopCustomer(
    @Query('year', ParseIntPipe) year: number,
    @Query('month', ParseIntPipe) month: number,
  ) {
    return this.appService.topCustomers(year, month);
  }

  @Get('/top-customer-details')
  getTopCustomerDetail(
    @Query('year', ParseIntPipe) year: number,
    @Query('month', ParseIntPipe) month: number,
    @Query('customerId', ParseIntPipe) customerId: number,
  ) {
    return this.appService.topCustomerDetail(customerId, year, month);
  }

  @Get('/yearly')
  getYearlyTotal(
    @Query('year', ParseIntPipe) year: number,
    @Query('branch', ParseIntPipe) branch: number
  ) {

    if (!!!year) {
      year = (new Date()).getFullYear()
    }

    if (!!!branch) {
      branch = 0;
    }

    return this.appService.yearlyTotal(year, branch);
  }
}
